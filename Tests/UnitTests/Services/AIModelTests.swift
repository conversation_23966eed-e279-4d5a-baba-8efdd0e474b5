//
//  AIModelTests.swift
//  Pebl
//
//  Created by AI Assistant on 6/17/25.
//

import Foundation

/// Test class to demonstrate the enhanced AI categorization capabilities
class AIModelTests {
    
    static func runTests() {
        print("🧪 Testing Enhanced AI Categorization System")
        print(String(repeating: "=", count: 50))
        
        let aiModel = AIModel()
        let categoryManager = CategoryManager()
        
        // Test cases that demonstrate the improvements
        let testMessages = [
            // Movie examples with genre detection
            "Watch The Dark Knight - Batman movie",
            "Inception - Christopher Nolan sci-fi thriller",
            "The Hangover - comedy movie",
            "Titanic - romantic drama",
            "Get Out - horror thriller",
            "Free Solo - climbing documentary",

            // Shopping examples with category detection
            "Buy iPhone 15 Pro",
            "Order Nike running shoes",
            "Purchase The Great Gatsby book",
            "Get organic vegetables from Whole Foods",
            "Buy IKEA desk for home office",

            // URL examples
            "https://www.amazon.com/dp/B08N5WRWNW - AirPods Pro",
            "https://www.netflix.com/title/80057281 - Stranger Things",
            "https://stackoverflow.com/questions/12345 - Swift programming help",
            "https://www.nytimes.com/2023/12/01/technology/ai-news.html",
            "https://docs.swift.org/swift-book/ - <PERSON> documentation",

            // Task examples with priority/type detection
            "URGENT: Submit quarterly report by Friday",
            "Schedule dentist appointment",
            "Plan weekend trip to San Francisco",
            "Review code for iOS app project",
            "Buy groceries for dinner party",

            // Reading examples with type detection
            "Read 'Atomic Habits' by James Clear",
            "Check out this AI research paper on neural networks",
            "Read TechCrunch article about startup funding",
            "Study Swift programming tutorial",

            // NEW: Creative life project examples
            "Research character development techniques for my novel",
            "Outline plot structure for fantasy book",
            "Find beta readers for manuscript",
            "Look into self-publishing options",

            "Schedule house viewing for Saturday",
            "Get pre-approved for mortgage",
            "Research neighborhood schools",
            "Contact real estate lawyer",

            "Buy prenatal vitamins",
            "Research pediatricians in area",
            "Set up baby registry",
            "Plan nursery layout",

            "Book engagement photographer",
            "Research wedding venues",
            "Create guest list spreadsheet",
            "Schedule cake tasting",

            "Update resume for career change",
            "Network with people in tech industry",
            "Take online course in data science",
            "Practice coding interview questions",
        ]
        
        print("\n📝 Testing Message Categorization:")
        print(String(repeating: "-", count: 30))
        
        for (index, message) in testMessages.enumerated() {
            print("\n\(index + 1). Message: \"\(message)\"")
            
            // Test the simplified categorization
            let availableCategories = categoryManager.getAllCategoryNames()
            let categoryName = aiModel.categorizeMessage(message, availableCategories: availableCategories)
            print("   ✅ Category: \(categoryName)")

            // Test subfolder suggestion if category exists
            if let existingCategory = categoryManager.findCategory(named: categoryName) {
                // Add some dummy messages to meet the 5-message threshold
                for i in 1...5 {
                    existingCategory.addMessage("Dummy message \(i)")
                }

                let subfolderResult = aiModel.shouldCreateSubfolder(for: message, in: existingCategory)
                if subfolderResult.shouldCreate, let subfolderName = subfolderResult.subfolderName {
                    print("   📁 Suggested Subfolder: \(subfolderName)")
                }
            }
            
            // Test URL analysis if message contains URLs
            let urls = aiModel.extractURLsFromMessage(message)
            if !urls.isEmpty {
                for url in urls {
                    let urlContext = aiModel.analyzeURLContent(url)
                    print("   🔗 URL Context (AI Analysis): \(urlContext)")
                }
            }
        }
        
        print("\n\n🎯 Key Improvements Demonstrated:")
        print(String(repeating: "-", count: 40))
        print("✅ Enhanced prompts with better context understanding")
        print("✅ URL domain analysis for better categorization")
        print("✅ Smart subfolder creation based on content patterns")
        print("✅ Context-aware categorization considering existing structure")
        print("✅ Preference for existing categories over creating new ones")
        print("✅ Genre-based movie categorization")
        print("✅ Type-based shopping categorization")
        print("✅ Priority-based task categorization")
        
        print("\n\n📋 Subfolder Creation Rules:")
        print(String(repeating: "-", count: 35))
        print("• Movies: Comedy, Action & Adventure, Drama & Romance, Horror & Thriller, Documentaries")
        print("• Shopping: Clothing & Fashion, Electronics, Books, Food & Grocery, Home & Garden")
        print("• To-Do: Urgent, Work, Personal")
        print("• To-Read: Articles & Blogs, Books, Research & Papers")
        print("• Minimum 5 messages in current category (excluding subcategories) before creating subfolders")
        
        print("\n\n🌐 Enhanced URL Analysis:")
        print(String(repeating: "-", count: 30))
        print("• AI-powered domain analysis (no hardcoded website lists)")
        print("• Dynamic categorization based on URL patterns and structure")
        print("• Content analysis option for deeper understanding")
        print("• Categories: shopping, entertainment, news, learning, work, health, travel, etc.")
        print("• Future-proof: works with any website automatically")

        // Test enhanced URL analysis
        print("\n\n🔍 Testing Enhanced URL Analysis:")
        print(String(repeating: "-", count: 40))
        testEnhancedURLAnalysis(aiModel: aiModel)

        // Test category optimization
        print("\n\n🔍 Testing Category Optimization:")
        print(String(repeating: "-", count: 40))
        testCategoryOptimization(aiModel: aiModel, categoryManager: categoryManager)
    }

    static func testCategoryOptimization(aiModel: AIModel, categoryManager: CategoryManager) {
        // Create a test category with multiple messages to analyze
        let bookWritingCategory = categoryManager.addRootCategory(name: "Book Writing", sfSymbol: "book.closed")

        // Add sample messages that should suggest natural subfolders
        let bookMessages = [
            "Research medieval history for fantasy setting",
            "Develop main character backstory",
            "Outline three-act structure",
            "Write first chapter draft",
            "Research publishing options",
            "Create character relationship map",
            "Plot timeline for story events",
            "Edit chapter 2 for pacing",
            "Find beta readers for feedback",
            "Research book cover design"
        ]

        for message in bookMessages {
            bookWritingCategory.addMessage(message)
        }

        print("📚 Analyzing 'Book Writing' category with \(bookMessages.count) messages...")
        let suggestions = aiModel.analyzeCategoryForOptimization(bookWritingCategory)

        if !suggestions.isEmpty {
            print("   💡 Suggested subfolders:")
            for suggestion in suggestions {
                print("      • \(suggestion)")
            }
        } else {
            print("   ℹ️ No subfolder suggestions (category may need more diverse content)")
        }

        // Test dynamic subfolder creation
        print("\n🎯 Testing Dynamic Subfolder Creation:")
        let newMessage = "Create detailed character profiles for antagonist"
        let subfolderResult = aiModel.shouldCreateSubfolder(for: newMessage, in: bookWritingCategory)

        if subfolderResult.shouldCreate, let subfolderName = subfolderResult.subfolderName {
            print("   📁 Would create subfolder: '\(subfolderName)' for message: '\(newMessage)'")
        } else {
            print("   ➡️ Would add directly to main category: '\(newMessage)'")
        }
    }

    static func testEnhancedURLAnalysis(aiModel: AIModel) {
        let testURLs = [
            "https://newstartup-shop.com/products/gadget",
            "https://learn.techplatform.io/course/swift-programming",
            "https://emerging-news.com/2024/ai-breakthrough",
            "https://johndoe.blog/travel-adventures",
            "https://fitness-tracker.app/workout-plans",
            "https://unknown-domain.xyz/some-content"
        ]

        print("Testing AI-powered URL analysis (no hardcoded lists):")

        for url in testURLs {
            let category = aiModel.analyzeURLContent(url)
            print("   🔗 \(url)")
            print("      → AI categorized as: \(category)")
        }

        print("\n💡 Key Advantages:")
        print("   ✅ No hardcoded website lists to maintain")
        print("   ✅ Works with any new website automatically")
        print("   ✅ AI understands URL patterns and structure")
        print("   ✅ Future-proof and maintenance-free")
        print("   ✅ More accurate than keyword matching")
    }

    static func testSFSymbolGeneration() {
        print("\n🎨 Testing SF Symbol Generation")
        print(String(repeating: "-", count: 40))

        let aiModel = AIModel()
        let testCategories = [
            "Shopping": "cart",
            "Movies to Watch": "film",
            "To-Do": "checkmark",
            "Books to Read": "book",
            "Travel Plans": "airplane"
        ]

        for (category, expectedSymbol) in testCategories {
            let symbol = aiModel.getSFSymbolForCategory(category)
            print("   \(category): \(symbol)")

            // Test that quotes are properly cleaned
            if symbol.contains("\"") || symbol.contains("'") || symbol.contains("`") {
                print("     ❌ Symbol contains quotes: \(symbol)")
            } else {
                print("     ✅ Symbol properly cleaned")
            }

            // Basic validation
            if symbol.contains(expectedSymbol) || symbol == "folder.fill" {
                print("     ✅ Valid symbol")
            } else {
                print("     ⚠️ Unexpected symbol")
            }
        }

        // Test quote cleaning specifically
        print("\n🧹 Testing Quote Cleaning:")
        // Simulate responses with quotes (for testing purposes)
        let quotedResponses = ["\"cart.fill\"", "'book.circle'", "`film.fill`", "  folder.fill  "]
        for response in quotedResponses {
            let cleaned = response
                .trimmingCharacters(in: .whitespacesAndNewlines)
                .trimmingCharacters(in: CharacterSet(charactersIn: "\"'`"))
            print("   '\(response)' → '\(cleaned)'")
        }

        print("\n✅ SF Symbol generation test completed")
    }
}
