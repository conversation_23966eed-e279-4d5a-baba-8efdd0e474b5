//
//  FileStorageServiceTests.swift
//  PeblTests
//
//  Created by AI Assistant on 7/2/25.
//

import XCTest
@testable import Pebl

class FileStorageServiceTests: XCTestCase {
    
    var storageService: FileStorageService!
    let testFileName = "test_file.json"
    
    override func setUp() {
        super.setUp()
        storageService = FileStorageService.shared
        
        // Clean up any existing test files
        try? storageService.delete(testFileName)
    }
    
    override func tearDown() {
        // Clean up test files
        try? storageService.delete(testFileName)
        super.tearDown()
    }
    
    func testSaveAndLoadObject() throws {
        // Given
        let testData = TestData(name: "Test", value: 42)
        
        // When
        try storageService.save(testData, to: testFileName)
        let loadedData = try storageService.load(TestData.self, from: testFileName)
        
        // Then
        XCTAssertEqual(loadedData.name, testData.name)
        XCTAssertEqual(loadedData.value, testData.value)
    }
    
    func testFileExists() throws {
        // Given
        let testData = TestData(name: "Test", value: 42)
        
        // When - file doesn't exist initially
        XCTAssertFalse(storageService.fileExists(testFileName))
        
        // When - save file
        try storageService.save(testData, to: testFileName)
        
        // Then
        XCTAssertTrue(storageService.fileExists(testFileName))
    }
    
    func testDeleteFile() throws {
        // Given
        let testData = TestData(name: "Test", value: 42)
        try storageService.save(testData, to: testFileName)
        XCTAssertTrue(storageService.fileExists(testFileName))
        
        // When
        try storageService.delete(testFileName)
        
        // Then
        XCTAssertFalse(storageService.fileExists(testFileName))
    }
    
    func testCreateBackup() throws {
        // Given
        let testData = TestData(name: "Test", value: 42)
        try storageService.save(testData, to: testFileName)
        
        // When
        let backupFileName = try storageService.createBackup(of: testFileName)
        
        // Then
        XCTAssertTrue(storageService.fileExists(backupFileName))
        XCTAssertTrue(backupFileName.hasPrefix(AppConfig.backupFilePrefix))
        
        // Verify backup content
        let backupData = try storageService.load(TestData.self, from: backupFileName)
        XCTAssertEqual(backupData.name, testData.name)
        XCTAssertEqual(backupData.value, testData.value)
        
        // Clean up
        try storageService.delete(backupFileName)
    }
    
    func testListBackups() throws {
        // Given
        let testData = TestData(name: "Test", value: 42)
        try storageService.save(testData, to: testFileName)
        
        // When
        let backup1 = try storageService.createBackup(of: testFileName)
        let backup2 = try storageService.createBackup(of: testFileName)
        
        let backups = storageService.listBackups()
        
        // Then
        XCTAssertTrue(backups.contains(backup1))
        XCTAssertTrue(backups.contains(backup2))
        XCTAssertGreaterThanOrEqual(backups.count, 2)
        
        // Clean up
        try storageService.delete(backup1)
        try storageService.delete(backup2)
    }
    
    func testCleanupOldBackups() throws {
        // Given
        let testData = TestData(name: "Test", value: 42)
        try storageService.save(testData, to: testFileName)
        
        // Create multiple backups
        var backupFiles: [String] = []
        for _ in 0..<7 {
            let backup = try storageService.createBackup(of: testFileName)
            backupFiles.append(backup)
            // Small delay to ensure different timestamps
            Thread.sleep(forTimeInterval: 0.01)
        }
        
        // When
        storageService.cleanupOldBackups(keepCount: 3)
        
        // Then
        let remainingBackups = storageService.listBackups()
        let testBackups = remainingBackups.filter { backupFiles.contains($0) }
        XCTAssertLessThanOrEqual(testBackups.count, 3)
        
        // Clean up remaining backups
        for backup in remainingBackups {
            if backupFiles.contains(backup) {
                try? storageService.delete(backup)
            }
        }
    }
    
    func testGetFileURL() {
        // When
        let fileURL = storageService.getFileURL(for: testFileName)
        
        // Then
        XCTAssertNotNil(fileURL)
        XCTAssertTrue(fileURL!.lastPathComponent == testFileName)
    }
    
    func testLoadNonExistentFile() {
        // When/Then
        XCTAssertThrowsError(try storageService.load(TestData.self, from: "nonexistent.json")) { error in
            XCTAssertTrue(error is StorageError)
            XCTAssertEqual(error as? StorageError, StorageError.fileNotFound)
        }
    }
    
    func testDeleteNonExistentFile() {
        // When/Then
        XCTAssertThrowsError(try storageService.delete("nonexistent.json")) { error in
            XCTAssertTrue(error is StorageError)
            XCTAssertEqual(error as? StorageError, StorageError.fileNotFound)
        }
    }
}

// MARK: - Test Data

private struct TestData: Codable, Equatable {
    let name: String
    let value: Int
}
