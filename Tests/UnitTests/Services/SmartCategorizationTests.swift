//
//  SmartCategorizationTests.swift
//  Pebl
//
//  Created by AI Assistant on 6/18/25.
//

import Foundation

/// Tests for smart categorization that both creates new categories AND handles edge cases
class SmartCategorizationTests {
    
    static func runSmartCategorizationTests() {
        print("🧠 SMART CATEGORIZATION TESTS")
        print(String(repeating: "=", count: 60))
        print("Testing intelligent new category creation + robust edge case handling")
        
        let aiModel = AIModel()
        let categoryManager = CategoryManager()
        
        // Setup basic categories
        let basicCategories = [
            ("To-Do", "checkmark.square"),
            ("Shopping", "cart"),
            ("Movies to Watch", "film"),
            ("To-Read", "book.circle")
        ]
        
        for (name, symbol) in basicCategories {
            _ = categoryManager.addRootCategory(name: name, sfSymbol: symbol)
        }
        
        let availableCategories = categoryManager.getAllCategoryNames()
        
        // Test both behaviors
        testNewCategoryCreation(aiModel: aiModel, availableCategories: availableCategories)
        testEdgeCaseHandling(aiModel: aiModel, availableCategories: availableCategories)
        testMixedScenarios(aiModel: aiModel, availableCategories: availableCategories)
        
        print("\n\n🎉 SMART CATEGORIZATION TESTS COMPLETED!")
        print("✅ Creates new categories for major projects")
        print("✅ Handles edge cases gracefully")
        print("✅ Best of both worlds!")
    }
    
    static func testNewCategoryCreation(aiModel: AIModel, availableCategories: [String]) {
        print("\n🆕 Testing New Category Creation")
        print(String(repeating: "-", count: 50))
        print("These should create NEW categories for major life projects:")
        
        let newCategoryTestCases = [
            // Major life projects that should create new categories
            ("Research wedding venues for summer ceremony", "Wedding Planning"),
            ("Schedule house viewing for Saturday morning", "House Buying"),
            ("Buy prenatal vitamins and schedule doctor appointment", "Baby Planning"),
            ("Outline chapter 3 of my fantasy novel", "Book Writing"),
            ("Update resume for career transition to tech", "Career Change"),
            ("Research graduate programs in computer science", "Education Planning"),
            ("Plan 2-week European vacation itinerary", "Travel Planning"),
            ("Set up home gym equipment in basement", "Fitness Journey"),
            ("Research business loan options for startup", "Business Startup"),
            ("Schedule consultation with wedding photographer", "Wedding Planning") // Should reuse existing
        ]
        
        var createdCategories: Set<String> = []
        
        for (index, (message, expectedCategory)) in newCategoryTestCases.enumerated() {
            let result = aiModel.categorizeMessage(message, availableCategories: Array(availableCategories) + Array(createdCategories))
            
            let isNewCategory = !availableCategories.contains(result) && !createdCategories.contains(result)
            let isExpectedCategory = result.lowercased().contains(expectedCategory.lowercased()) || 
                                   expectedCategory.lowercased().contains(result.lowercased())
            
            if isNewCategory {
                createdCategories.insert(result)
            }
            
            let status = isExpectedCategory ? "✅" : "⚠️"
            let categoryType = isNewCategory ? "NEW" : "EXISTING"
            
            print("   \(index + 1). \(status) \(categoryType): '\(result)'")
            print("      Message: \"\(message)\"")
            
            if !isExpectedCategory {
                print("      Expected something like: '\(expectedCategory)'")
            }
        }
        
        print("\n   📊 Summary:")
        print("      New categories created: \(createdCategories.count)")
        print("      Categories: \(createdCategories.sorted().joined(separator: ", "))")
    }
    
    static func testEdgeCaseHandling(aiModel: AIModel, availableCategories: [String]) {
        print("\n🛡️ Testing Edge Case Handling")
        print(String(repeating: "-", count: 50))
        print("These should NOT create invalid categories:")
        
        let edgeCaseTestCases = [
            // These should use existing categories, not create invalid ones
            "seven",
            "7",
            "123",
            "a",
            "hi",
            "ok",
            "yes",
            "?",
            "!",
            "...",
            "",
            "   ",
            "The provided message is not sufficient to categorize",
            "I cannot determine the category",
            "This message is unclear and ambiguous",
            "More context needed to categorize properly"
        ]
        
        for (index, testCase) in edgeCaseTestCases.enumerated() {
            let result = aiModel.categorizeMessage(testCase, availableCategories: availableCategories)
            let isValidExisting = availableCategories.contains(result)
            let isInvalidResponse = result.count > 30 || result.contains("provided") || result.contains("cannot")
            
            let status = isValidExisting && !isInvalidResponse ? "✅" : "❌"
            let displayMessage = testCase.isEmpty ? "(empty)" : testCase
            
            print("   \(index + 1). \(status) '\(displayMessage)' → '\(result)'")
            
            if !isValidExisting {
                print("      ⚠️  Should return existing category, not: '\(result)'")
            }
            
            if isInvalidResponse {
                print("      ⚠️  Invalid response detected!")
            }
        }
    }
    
    static func testMixedScenarios(aiModel: AIModel, availableCategories: [String]) {
        print("\n🎯 Testing Mixed Scenarios")
        print(String(repeating: "-", count: 50))
        print("Testing realistic mix of existing categories vs new categories:")
        
        let mixedTestCases = [
            // Should use existing categories
            ("Buy groceries for dinner", "Shopping", false),
            ("Watch The Dark Knight movie", "Movies to Watch", false),
            ("Read Atomic Habits book", "To-Read", false),
            ("Schedule dentist appointment", "To-Do", false),
            
            // Should create new categories
            ("Research wedding dress designers", "Wedding Planning", true),
            ("Schedule house inspection", "House Buying", true),
            ("Write character backstory for novel", "Book Writing", true),
            
            // Borderline cases - could go either way
            ("Plan birthday party for mom", "To-Do or Party Planning", false), // Could be either
            ("Research vacation destinations", "To-Do or Travel Planning", false), // Could be either
        ]
        
        for (index, (message, expectedType, shouldCreateNew)) in mixedTestCases.enumerated() {
            let result = aiModel.categorizeMessage(message, availableCategories: availableCategories)
            let isExisting = availableCategories.contains(result)
            let isNew = !isExisting
            let isValid = availableCategories.contains(result) || (isNew && result.count >= 3 && result.count <= 30)
            
            let status = isValid ? "✅" : "❌"
            let categoryType = isExisting ? "EXISTING" : "NEW"
            let expectation = shouldCreateNew ? "Expected NEW" : "Expected EXISTING"
            
            print("   \(index + 1). \(status) \(categoryType): '\(result)'")
            print("      Message: \"\(message)\"")
            print("      \(expectation) (\(expectedType))")
            
            if !isValid {
                print("      ⚠️  Invalid category response!")
            }
        }
    }
    
    static func demonstrateSmartBehavior() {
        print("\n🎯 SMART BEHAVIOR DEMONSTRATION")
        print(String(repeating: "=", count: 60))
        
        let aiModel = AIModel()
        let availableCategories = ["To-Do", "Shopping", "Movies to Watch", "To-Read"]
        
        print("Available categories: \(availableCategories.joined(separator: ", "))")
        print()
        
        // Demonstrate the two behaviors
        let testCases = [
            ("seven", "Edge case → existing category"),
            ("Buy groceries", "Clear intent → existing category"),
            ("Research wedding venues", "Major project → new category"),
            ("I cannot categorize this", "AI error → existing category"),
            ("Schedule house viewing", "Major project → new category"),
            ("?", "Edge case → existing category")
        ]
        
        for (message, description) in testCases {
            let result = aiModel.categorizeMessage(message, availableCategories: availableCategories)
            let isExisting = availableCategories.contains(result)
            let categoryType = isExisting ? "EXISTING" : "NEW"
            
            print("📝 \"\(message)\"")
            print("   → \(categoryType): '\(result)' (\(description))")
            print()
        }
        
        print("🎉 Perfect! The system:")
        print("✅ Creates new categories for major life projects")
        print("✅ Uses existing categories for clear matches")
        print("✅ Handles edge cases gracefully with existing categories")
        print("✅ Never creates invalid categories from AI errors")
    }
}
