import Foundation

// Demo script to test URL parsing functionality
class URLParsingDemo {
    
    static func runDemo() {
        print("🔗 URL Parsing Demo")
        print("==================")
        
        let testURLs = [
            ("https://www.imdb.com/title/tt0133093/", "Movies"),
            ("https://www.amazon.com/dp/B08N5WRWNW", "Shopping"),
            ("https://www.nytimes.com/2023/12/01/technology/ai-chatbots.html", "News"),
            ("https://github.com/apple/swift", "Tech"),
            ("https://www.youtube.com/watch?v=dQw4w9WgXcQ", "Entertainment")
        ]
        
        for (url, category) in testURLs {
            print("\n📱 Testing URL: \(url)")
            print("📂 Category: \(category)")
            
            // Test URL parsing
            let parsed = MessageParser.parseURL(url, for: category)
            print("✨ Main Message: \(parsed.mainMessage)")
            print("🔗 Subcontent: \(parsed.subcontent ?? "None")")
            
            // Test Message creation
            let message = Message(text: url, categoryName: category)
            print("📝 Message Display:")
            print("   Main: \(message.mainMessage)")
            print("   Sub: \(message.subcontent ?? "None")")
            print("   Original: \(message.text)")
        }
        
        print("\n✅ Demo completed!")
    }
}
