//
//  Message.swift
//  Pebl
//
//  Created by <PERSON><PERSON> on 6/17/25.
//

import Foundation
import UniformTypeIdentifiers
import CoreTransferable

/// Represents a message with completion status and parsed content for modern UI
struct Message: Codable, Identifiable, Hashable, Transferable {
    var id = UUID()
    var text: String
    var isCompleted: Bool = false
    var timestamp: Date = Date()

    // Parsed content for modern UI display
    var mainMessage: String
    var subcontent: String?

    init(text: String, isCompleted: Bool = false, categoryName: String = "", timestamp: Date = Date()) {
        self.text = text
        self.isCompleted = isCompleted
        self.timestamp = timestamp

        // Check if the message is a URL and parse accordingly
        if text.lowercased().hasPrefix("http://") || text.lowercased().hasPrefix("https://") {
            let parsed = MessageParser.parseURL(text, for: categoryName)
            self.mainMessage = parsed.mainMessage
            self.subcontent = parsed.subcontent
        } else {
            // Parse regular message for clean display
            let parsed = MessageParser.parseMessage(text, for: categoryName)
            self.mainMessage = parsed.mainMessage
            self.subcontent = parsed.subcontent
        }
    }

    // Legacy initializer for existing code
    init(text: String, isCompleted: Bool = false) {
        self.text = text
        self.isCompleted = isCompleted
        self.timestamp = Date()

        // Check if the message is a URL and parse accordingly
        if text.lowercased().hasPrefix("http://") || text.lowercased().hasPrefix("https://") {
            let parsed = MessageParser.parseURL(text, for: "")
            self.mainMessage = parsed.mainMessage
            self.subcontent = parsed.subcontent
        } else {
            // Use generic parsing for legacy messages
            let parsed = MessageParser.quickParse(text)
            self.mainMessage = parsed.main
            self.subcontent = parsed.sub
        }
    }

    // Update parsed content when category changes
    mutating func updateParsedContent(for categoryName: String) {
        if text.lowercased().hasPrefix("http://") || text.lowercased().hasPrefix("https://") {
            let parsed = MessageParser.parseURL(text, for: categoryName)
            self.mainMessage = parsed.mainMessage
            self.subcontent = parsed.subcontent
        } else {
            let parsed = MessageParser.parseMessage(text, for: categoryName)
            self.mainMessage = parsed.mainMessage
            self.subcontent = parsed.subcontent
        }
    }

    // Update message text and re-parse content
    mutating func updateText(_ newText: String, for categoryName: String) {
        self.text = newText
        updateParsedContent(for: categoryName)
    }

    // MARK: - Codable Implementation for Backward Compatibility

    enum CodingKeys: String, CodingKey {
        case id, text, isCompleted, timestamp, mainMessage, subcontent
    }

    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)

        id = try container.decode(UUID.self, forKey: .id)
        text = try container.decode(String.self, forKey: .text)
        isCompleted = try container.decode(Bool.self, forKey: .isCompleted)

        // Handle timestamp with backward compatibility
        if let decodedTimestamp = try? container.decode(Date.self, forKey: .timestamp) {
            timestamp = decodedTimestamp
        } else {
            // For old messages without timestamp, use a default date
            timestamp = Date()
        }

        mainMessage = try container.decode(String.self, forKey: .mainMessage)
        subcontent = try? container.decode(String.self, forKey: .subcontent)
    }

    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)

        try container.encode(id, forKey: .id)
        try container.encode(text, forKey: .text)
        try container.encode(isCompleted, forKey: .isCompleted)
        try container.encode(timestamp, forKey: .timestamp)
        try container.encode(mainMessage, forKey: .mainMessage)
        try container.encodeIfPresent(subcontent, forKey: .subcontent)
    }

    // MARK: - Transferable Implementation

    static var transferRepresentation: some TransferRepresentation {
        CodableRepresentation(contentType: .data)
    }

    // MARK: - Hashable Implementation

    func hash(into hasher: inout Hasher) {
        hasher.combine(id)
    }

    static func == (lhs: Message, rhs: Message) -> Bool {
        return lhs.id == rhs.id
    }
}

// MARK: - Message Extensions

extension Message {
    /// Check if the message has expired based on the given expiration days
    func isExpired(expirationDays: Int) -> Bool {
        let expirationDate = Calendar.current.date(byAdding: .day, value: expirationDays, to: timestamp) ?? Date()
        return Date() > expirationDate
    }
    
    /// Get a formatted timestamp string
    var formattedTimestamp: String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .short
        return formatter.string(from: timestamp)
    }

    /// Get a relative time string (e.g., "2 hours ago", "Yesterday")
    var relativeTimeString: String {
        let formatter = RelativeDateTimeFormatter()
        formatter.unitsStyle = .abbreviated
        return formatter.localizedString(for: timestamp, relativeTo: Date())
    }
    
    /// Check if this is a URL message
    var isURL: Bool {
        return text.lowercased().hasPrefix("http://") || text.lowercased().hasPrefix("https://")
    }

    /// Check if the message is older than the specified number of days
    func isOlderThan(days: Int) -> Bool {
        let calendar = Calendar.current
        guard let cutoffDate = calendar.date(byAdding: .day, value: -days, to: Date()) else {
            return false
        }
        return timestamp < cutoffDate
    }
}
