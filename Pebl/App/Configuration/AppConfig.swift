//
//  AppConfig.swift
//  Pebl
//
//  Created by <PERSON><PERSON> on 6/17/25.
//

import Foundation

/// Central configuration for the Pebl application
struct AppConfig {
    
    // MARK: - App Information
    
    static let appName = "Pebl"
    static let appVersion = "1.0.0"
    static let appDescription = "A smart personal assistant iOS app that helps you organize your thoughts and tasks into meaningful categories."
    
    // MARK: - File Storage
    
    static let categoriesFileName = "categories.json"
    static let settingsFileName = "settings.json"
    static let backupFilePrefix = "pebl_backup_"
    
    // MARK: - Message Management
    
    /// Default expiration time for messages (in days)
    static let defaultMessageExpirationDays = 60
    
    /// Maximum number of messages to display in a category before pagination
    static let maxMessagesPerPage = 100
    
    /// Maximum length for a message text
    static let maxMessageLength = 1000
    
    /// Minimum length for a message to be considered valid
    static let minMessageLength = 1
    
    // MARK: - Categorization Settings
    
    /// Minimum confidence threshold for AI categorization
    static let categorizationConfidenceThreshold = 0.7
    
    /// Maximum number of categories to suggest for a message
    static let maxCategorySuggestions = 3
    
    /// Timeout for AI categorization requests (in seconds)
    static let categorizationTimeoutSeconds = 10.0
    
    /// Maximum number of retries for failed categorization
    static let maxCategorizationRetries = 3
    
    // MARK: - UI Configuration
    
    /// Animation duration for UI transitions
    static let defaultAnimationDuration = 0.3
    
    /// Debounce delay for search input (in seconds)
    static let searchDebounceDelay = 0.5
    
    /// Maximum number of recent searches to remember
    static let maxRecentSearches = 10
    
    // MARK: - Performance Settings
    
    /// Maximum number of concurrent categorization operations
    static let maxConcurrentCategorizations = 3
    
    /// Cache size for parsed messages
    static let messageCacheSize = 500
    
    /// Background task timeout (in seconds)
    static let backgroundTaskTimeout = 30.0
    
    // MARK: - Feature Flags
    
    /// Enable/disable automatic subcategory creation
    static let enableAutoSubcategoryCreation = true
    
    /// Enable/disable message expiration
    static let enableMessageExpiration = true
    
    /// Enable/disable hashtag categorization
    static let enableHashtagCategorization = true
    
    /// Enable/disable URL content extraction
    static let enableURLContentExtraction = true
    
    /// Enable/disable automatic cleanup of expired messages
    static let enableAutoCleanup = true
    
    // MARK: - Debug Settings
    
    #if DEBUG
    /// Enable verbose logging in debug builds
    static let enableVerboseLogging = true
    
    /// Enable test data generation
    static let enableTestDataGeneration = true
    
    /// Reduced timeouts for testing
    static let debugCategorizationTimeout = 5.0
    #else
    static let enableVerboseLogging = false
    static let enableTestDataGeneration = false
    #endif
    
    // MARK: - URL Patterns
    
    /// Common URL patterns for content extraction
    static let supportedURLPatterns = [
        "youtube.com",
        "amazon.com",
        "netflix.com",
        "imdb.com",
        "goodreads.com",
        "medium.com",
        "github.com",
        "stackoverflow.com"
    ]
    
    /// URL schemes that should be handled specially
    static let specialURLSchemes = [
        "mailto:",
        "tel:",
        "sms:",
        "facetime:",
        "maps:"
    ]
    
    // MARK: - Validation Rules
    
    /// Valid characters for category names
    static let validCategoryNameCharacters = CharacterSet.alphanumerics.union(CharacterSet(charactersIn: " -_"))
    
    /// Maximum category name length
    static let maxCategoryNameLength = 50
    
    /// Minimum category name length
    static let minCategoryNameLength = 1
    
    /// Reserved category names that cannot be used
    static let reservedCategoryNames = [
        "system",
        "temp",
        "cache",
        "backup",
        "settings"
    ]
    
    // MARK: - Helper Methods
    
    /// Check if a category name is valid
    static func isValidCategoryName(_ name: String) -> Bool {
        let trimmedName = name.trimmingCharacters(in: .whitespacesAndNewlines)
        
        guard trimmedName.count >= minCategoryNameLength,
              trimmedName.count <= maxCategoryNameLength else {
            return false
        }
        
        guard !reservedCategoryNames.contains(trimmedName.lowercased()) else {
            return false
        }
        
        return trimmedName.rangeOfCharacter(from: validCategoryNameCharacters.inverted) == nil
    }
    
    /// Check if a message is valid
    static func isValidMessage(_ text: String) -> Bool {
        let trimmedText = text.trimmingCharacters(in: .whitespacesAndNewlines)
        return trimmedText.count >= minMessageLength && trimmedText.count <= maxMessageLength
    }
    
    /// Get file URL for a given filename in the documents directory
    static func getFileURL(for filename: String) -> URL? {
        guard let documentsDirectory = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first else {
            return nil
        }
        return documentsDirectory.appendingPathComponent(filename)
    }
}
