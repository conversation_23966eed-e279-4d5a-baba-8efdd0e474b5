//
//  DefaultCategories.swift
//  Pebl
//
//  Created by <PERSON><PERSON> on 6/17/25.
//

import Foundation

/// Configuration for default categories and their properties
struct DefaultCategoriesConfig {
    
    /// Default categories that are created when the app first launches
    static let defaultCategories: [(name: String, sfSymbol: String)] = [
        ("To-Read", "book.circle"),
        ("Shopping", "cart"),
        ("To-Do", "checkmark.square"),
        ("Movies to Watch", "film"),
        ("Appointments", "calendar")
    ]
    
    /// Default SF Symbols for common category types
    static let categorySymbols: [String: String] = [
        // Reading & Learning
        "reading": "book.circle",
        "books": "book.closed",
        "articles": "doc.text",
        "research": "magnifyingglass",
        "study": "graduationcap",
        
        // Shopping & Commerce
        "shopping": "cart",
        "groceries": "basket",
        "electronics": "desktopcomputer",
        "clothing": "tshirt",
        "home": "house",
        
        // Tasks & Productivity
        "tasks": "checkmark.square",
        "work": "briefcase",
        "projects": "folder",
        "meetings": "person.2",
        "deadlines": "clock",
        
        // Entertainment
        "movies": "film",
        "tv": "tv",
        "music": "music.note",
        "games": "gamecontroller",
        "podcasts": "mic",
        
        // Health & Fitness
        "health": "heart",
        "fitness": "figure.run",
        "medical": "cross.case",
        "nutrition": "leaf",
        
        // Travel & Places
        "travel": "airplane",
        "restaurants": "fork.knife",
        "places": "location",
        "hotels": "bed.double",
        
        // Finance
        "finance": "dollarsign.circle",
        "bills": "creditcard",
        "investments": "chart.line.uptrend.xyaxis",
        
        // Personal
        "personal": "person.circle",
        "family": "person.3",
        "friends": "person.2.circle",
        "hobbies": "paintbrush",
        
        // Events & Time
        "appointments": "calendar",
        "events": "calendar.badge.plus",
        "reminders": "bell",
        "birthdays": "gift",
        
        // Technology
        "apps": "app.badge",
        "software": "laptopcomputer",
        "coding": "chevron.left.forwardslash.chevron.right",
        
        // Default fallback
        "default": "folder.fill"
    ]
    
    /// Get appropriate SF Symbol for a category name
    static func getSymbol(for categoryName: String) -> String {
        let lowercaseName = categoryName.lowercased()
        
        // Direct match
        if let symbol = categorySymbols[lowercaseName] {
            return symbol
        }
        
        // Partial match
        for (key, symbol) in categorySymbols {
            if lowercaseName.contains(key) || key.contains(lowercaseName) {
                return symbol
            }
        }
        
        // Default fallback
        return categorySymbols["default"] ?? "folder.fill"
    }
}


