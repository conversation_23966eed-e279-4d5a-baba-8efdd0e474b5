//
//  DefaultCategories.swift
//  Pebl
//
//  Created by <PERSON><PERSON> on 6/17/25.
//

import Foundation

/// Configuration for default categories and their properties
struct DefaultCategoriesConfig {
    
    /// Default categories that are created when the app first launches
    static let defaultCategories: [(name: String, sfSymbol: String)] = [
        ("To-Read", "book.circle"),
        ("Shopping", "cart"),
        ("To-Do", "checkmark.square"),
        ("Movies to Watch", "film"),
        ("Appointments", "calendar")
    ]
    
    /// Default SF Symbols for common category types
    static let categorySymbols: [String: String] = [
        // Reading & Learning
        "reading": "book.circle",
        "books": "book.closed",
        "articles": "doc.text",
        "research": "magnifyingglass",
        "study": "graduationcap",
        
        // Shopping & Commerce
        "shopping": "cart",
        "groceries": "basket",
        "electronics": "desktopcomputer",
        "clothing": "tshirt",
        "home": "house",
        
        // Tasks & Productivity
        "tasks": "checkmark.square",
        "work": "briefcase",
        "projects": "folder",
        "meetings": "person.2",
        "deadlines": "clock",
        
        // Entertainment
        "movies": "film",
        "tv": "tv",
        "music": "music.note",
        "games": "gamecontroller",
        "podcasts": "mic",
        
        // Health & Fitness
        "health": "heart",
        "fitness": "figure.run",
        "medical": "cross.case",
        "nutrition": "leaf",
        
        // Travel & Places
        "travel": "airplane",
        "restaurants": "fork.knife",
        "places": "location",
        "hotels": "bed.double",
        
        // Finance
        "finance": "dollarsign.circle",
        "bills": "creditcard",
        "investments": "chart.line.uptrend.xyaxis",
        
        // Personal
        "personal": "person.circle",
        "family": "person.3",
        "friends": "person.2.circle",
        "hobbies": "paintbrush",
        
        // Events & Time
        "appointments": "calendar",
        "events": "calendar.badge.plus",
        "reminders": "bell",
        "birthdays": "gift",
        
        // Technology
        "apps": "app.badge",
        "software": "laptopcomputer",
        "coding": "chevron.left.forwardslash.chevron.right",
        
        // Default fallback
        "default": "folder.fill"
    ]
    
    /// Get appropriate SF Symbol for a category name
    static func getSymbol(for categoryName: String) -> String {
        let lowercaseName = categoryName.lowercased()
        
        // Direct match
        if let symbol = categorySymbols[lowercaseName] {
            return symbol
        }
        
        // Partial match
        for (key, symbol) in categorySymbols {
            if lowercaseName.contains(key) || key.contains(lowercaseName) {
                return symbol
            }
        }
        
        // Default fallback
        return categorySymbols["default"] ?? "folder.fill"
    }
}

/// Configuration for subcategory patterns and thresholds
struct SubcategoryConfig {
    
    /// Minimum number of messages required before suggesting subcategory creation
    static let subcategoryThreshold = 5
    
    /// Maximum number of messages to analyze for subcategory suggestions
    static let maxMessagesForAnalysis = 20
    
    /// Common subcategory patterns for different category types
    static let subcategoryPatterns: [String: [String: [String]]] = [
        "movies": [
            "Comedy": ["comedy", "funny", "laugh", "humor"],
            "Action & Adventure": ["action", "adventure", "thriller", "suspense"],
            "Drama & Romance": ["drama", "romantic", "romance", "love"],
            "Horror & Thriller": ["horror", "scary", "fear", "thriller"],
            "Documentaries": ["documentary", "docu", "real", "factual"],
            "Sci-Fi & Fantasy": ["sci-fi", "fantasy", "space", "magic"]
        ],
        "shopping": [
            "Clothing & Fashion": ["cloth", "shirt", "dress", "shoe", "fashion", "wear"],
            "Electronics": ["electronic", "phone", "computer", "gadget", "tech", "device"],
            "Books & Media": ["book", "read", "magazine", "media"],
            "Food & Grocery": ["food", "grocery", "kitchen", "cook", "eat"],
            "Home & Garden": ["home", "furniture", "decor", "garden", "house"],
            "Health & Beauty": ["health", "beauty", "cosmetic", "skincare"]
        ],
        "tasks": [
            "Urgent": ["urgent", "asap", "important", "priority", "critical"],
            "Work": ["work", "office", "meeting", "business", "professional"],
            "Personal": ["personal", "home", "family", "private"],
            "Health": ["health", "medical", "doctor", "appointment"],
            "Finance": ["money", "bill", "payment", "bank", "finance"]
        ],
        "reading": [
            "Articles & Blogs": ["article", "blog", "news", "post"],
            "Books": ["book", "novel", "literature"],
            "Research & Papers": ["research", "paper", "study", "academic"],
            "Technical": ["technical", "documentation", "manual", "guide"]
        ]
    ]
    
    /// Get suggested subcategories for a category based on its messages
    static func getSuggestedSubcategories(for categoryName: String, messages: [String]) -> [String] {
        let lowercaseCategoryName = categoryName.lowercased()
        
        guard let patterns = subcategoryPatterns[lowercaseCategoryName] else {
            return []
        }
        
        var suggestions: [String] = []
        let combinedMessages = messages.joined(separator: " ").lowercased()
        
        for (subcategoryName, keywords) in patterns {
            let matchCount = keywords.reduce(0) { count, keyword in
                count + combinedMessages.components(separatedBy: keyword).count - 1
            }
            
            if matchCount >= 2 { // Require at least 2 keyword matches
                suggestions.append(subcategoryName)
            }
        }
        
        return suggestions
    }
}
