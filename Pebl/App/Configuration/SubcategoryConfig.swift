//
//  SubcategoryConfig.swift
//  Pebl
//
//  Created by AI Assistant on 7/2/25.
//

import Foundation

/// Configuration for subcategory creation and management
struct SubcategoryConfig {
    
    // MARK: - Subcategory Creation Settings
    
    /// Minimum number of messages in a category before considering subcategory creation
    static let subcategoryThreshold = 5
    
    /// Maximum number of subcategories to create automatically
    static let maxAutoSubcategories = 10
    
    /// Enable automatic subcategory creation based on patterns
    static let enableAutoSubcategoryCreation = true
    
    // MARK: - Subcategory Patterns
    
    /// Predefined patterns for automatic subcategory creation
    static let subcategoryPatterns: [String: [String: [String]]] = [
        "Movies": [
            "Comedy": ["funny", "comedy", "humor", "laugh", "hilarious", "comic"],
            "Action": ["action", "adventure", "thriller", "fight", "chase", "explosion"],
            "Drama": ["drama", "emotional", "serious", "touching", "heartfelt"],
            "Horror": ["horror", "scary", "frightening", "terror", "spooky"],
            "Romance": ["romance", "love", "romantic", "relationship", "dating"]
        ],
        "Books": [
            "Fiction": ["novel", "story", "fiction", "fantasy", "sci-fi", "mystery"],
            "Non-Fiction": ["biography", "history", "science", "self-help", "business"],
            "Technical": ["programming", "coding", "development", "engineering", "tech"],
            "Educational": ["textbook", "learning", "study", "course", "academic"]
        ],
        "Shopping": [
            "Electronics": ["phone", "laptop", "computer", "gadget", "device", "tech"],
            "Clothing": ["shirt", "pants", "dress", "shoes", "fashion", "apparel"],
            "Home": ["furniture", "decor", "kitchen", "bedroom", "living room"],
            "Food": ["grocery", "food", "snack", "drink", "restaurant", "recipe"]
        ],
        "URLs": [
            "Articles": ["article", "blog", "news", "read", "story", "post"],
            "Videos": ["video", "watch", "youtube", "movie", "show", "stream"],
            "Shopping": ["buy", "purchase", "store", "shop", "product", "deal"],
            "Social": ["social", "facebook", "twitter", "instagram", "linkedin"]
        ],
        "To-Do": [
            "Work": ["work", "office", "meeting", "project", "deadline", "task"],
            "Personal": ["personal", "home", "family", "health", "hobby"],
            "Shopping": ["buy", "purchase", "get", "pick up", "order"],
            "Appointments": ["appointment", "doctor", "dentist", "meeting", "schedule"]
        ]
    ]
    
    // MARK: - Helper Methods
    
    /// Get subcategory patterns for a specific category
    static func getPatterns(for categoryName: String) -> [String: [String]]? {
        return subcategoryPatterns[categoryName]
    }
    
    /// Check if a category should have automatic subcategory creation
    static func shouldCreateSubcategories(for categoryName: String, messageCount: Int) -> Bool {
        guard enableAutoSubcategoryCreation else { return false }
        guard messageCount >= subcategoryThreshold else { return false }
        return subcategoryPatterns.keys.contains { key in
            categoryName.lowercased().contains(key.lowercased())
        }
    }
    
    /// Get suggested subcategory name based on message content
    static func getSuggestedSubcategory(for message: String, in categoryName: String) -> String? {
        guard let patterns = getPatterns(for: categoryName) else { return nil }
        
        let lowercaseMessage = message.lowercased()
        
        for (subcategoryName, keywords) in patterns {
            for keyword in keywords {
                if lowercaseMessage.contains(keyword.lowercased()) {
                    return subcategoryName
                }
            }
        }
        
        return nil
    }
}
