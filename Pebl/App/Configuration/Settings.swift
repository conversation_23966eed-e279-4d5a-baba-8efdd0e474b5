//
//  Settings.swift
//  Pebl
//
//  Created by AI Assistant on 6/26/25.
//

import Foundation

/// User settings and preferences for the app
struct AppSettings: Codable {
    var messageExpirationDays: Int = 60
    var isDarkModeEnabled: Bool = false
    var autoDeleteExpiredMessages: Bool = true
    
    // MARK: - Singleton Instance
    static let shared = SettingsManager()
}

/// Manages app settings persistence and access
class SettingsManager: ObservableObject {
    @Published var settings = AppSettings()

    private let storageService = SettingsStorageService.shared

    init() {
        loadSettings()
    }

    // MARK: - Persistence Methods

    func saveSettings() {
        do {
            try storageService.saveSettings(settings)
        } catch {
            print("Error saving settings: \(error)")
        }
    }

    func loadSettings() {
        // Try to migrate from UserDefaults first
        if let migratedSettings = storageService.migrateFromUserDefaults() {
            settings = migratedSettings
            return
        }

        // Load from file storage with fallback to defaults
        settings = storageService.loadSettingsWithFallback()
    }
    
    // MARK: - Convenience Methods
    
    func updateMessageExpirationDays(_ days: Int) {
        settings.messageExpirationDays = days
        saveSettings()
    }
    
    func toggleDarkMode() {
        settings.isDarkModeEnabled.toggle()
        saveSettings()
    }
    
    func updateAutoDeleteExpiredMessages(_ enabled: Bool) {
        settings.autoDeleteExpiredMessages = enabled
        saveSettings()
    }
}

// MARK: - Settings Extensions

extension AppSettings {
    /// Returns true if a message should be considered expired based on current settings
    func isMessageExpired(_ message: Message) -> Bool {
        return message.isOlderThan(days: messageExpirationDays)
    }
    
    /// Returns the cutoff date for message expiration
    var messageExpirationCutoffDate: Date {
        let calendar = Calendar.current
        return calendar.date(byAdding: .day, value: -messageExpirationDays, to: Date()) ?? Date()
    }
}
