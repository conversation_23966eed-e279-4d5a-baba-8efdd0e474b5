//
//  SettingsView.swift
//  Pebl
//
//  Created by AI Assistant on 6/26/25.
//

import SwiftUI

struct SettingsView: View {
    @ObservedObject var settingsManager: SettingsManager
    @Environment(\.presentationMode) var presentationMode
    
    // Local state for the stepper
    @State private var tempExpirationDays: Int = 60
    
    var body: some View {
        NavigationView {
            Form {
                Section(header: Text("Message Management")) {
                    VStack(alignment: .leading, spacing: 8) {
                        HStack {
                            Text("Message Expiration")
                                .font(.headline)
                            Spacer()
                            Text("\(tempExpirationDays) days")
                                .foregroundColor(.secondary)
                        }
                        
                        Stepper(
                            value: $tempExpirationDays,
                            in: 1...365,
                            step: 1
                        ) {
                            Text("Delete messages older than \(tempExpirationDays) days")
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                        .onChange(of: tempExpirationDays) { newValue in
                            settingsManager.updateMessageExpirationDays(newValue)
                        }
                    }
                    .padding(.vertical, 4)
                    
                    Toggle("Auto-delete expired messages", isOn: Binding(
                        get: { settingsManager.settings.autoDeleteExpiredMessages },
                        set: { settingsManager.updateAutoDeleteExpiredMessages($0) }
                    ))
                }
                
                Section(header: Text("Appearance")) {
                    Toggle("Dark Mode", isOn: Binding(
                        get: { settingsManager.settings.isDarkModeEnabled },
                        set: { _ in settingsManager.toggleDarkMode() }
                    ))
                }
                
                Section(header: Text("Information")) {
                    HStack {
                        Text("App Version")
                        Spacer()
                        Text("1.0.0")
                            .foregroundColor(.secondary)
                    }
                    
                    HStack {
                        Text("Build")
                        Spacer()
                        Text("1")
                            .foregroundColor(.secondary)
                    }
                }
            }
            .navigationTitle("Settings")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        presentationMode.wrappedValue.dismiss()
                    }
                }
            }
        }
        .onAppear {
            tempExpirationDays = settingsManager.settings.messageExpirationDays
        }
    }
}

struct SettingsView_Previews: PreviewProvider {
    static var previews: some View {
        SettingsView(settingsManager: SettingsManager())
    }
}
