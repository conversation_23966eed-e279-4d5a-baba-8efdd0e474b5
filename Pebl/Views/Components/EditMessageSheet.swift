//
//  EditMessageSheet.swift
//  Pebl
//
//  Created by AI Assistant on 7/1/25.
//

import SwiftUI

/// Sheet for editing an existing message
struct EditMessageSheet: View {
    @Binding var isPresented: Bool
    let message: Message
    let onSave: (String) -> Void
    
    @State private var editedText: String = ""
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                VStack(alignment: .leading, spacing: 8) {
                    Text("Edit Message")
                        .font(.headline)
                    
                    TextField("Enter your message...", text: $editedText, axis: .vertical)
                        .textFieldStyle(RoundedBorderTextFieldStyle())
                        .lineLimit(3...10)
                        .padding(.horizontal)
                }
                
                Spacer()
            }
            .padding()
            .navigationTitle("Edit Message")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    But<PERSON>("Cancel") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    <PERSON><PERSON>("Save") {
                        onSave(editedText)
                        dismiss()
                    }
                    .disabled(editedText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty)
                }
            }
        }
        .onAppear {
            editedText = message.text
        }
    }
}

#Preview {
    EditMessageSheet(
        isPresented: .constant(true),
        message: Message(text: "Sample message to edit"),
        onSave: { _ in }
    )
}
