import SwiftUI

struct SearchResultsView: View {
    @Binding var searchText: String
    @ObservedObject var categoryManager: CategoryManager
    let onBack: () -> Void
    let onNavigateToCategory: (Category) -> Void
    
    @State private var searchResults: [(message: Message, category: Category)] = []
    
    var body: some View {
        VStack {
            HStack {
                TextField("Search messages...", text: $searchText)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
                    .padding()
                    .onChange(of: searchText) { _ in
                        performSearch()
                    }
                
                Button("Cancel") {
                    onBack()
                }
                .padding(.trailing)
            }
            
            if searchResults.isEmpty && !searchText.isEmpty {
                Text("No results found")
                    .foregroundColor(.secondary)
                    .padding(.top, 40)
                Spacer()
            } else if searchResults.isEmpty && searchText.isEmpty {
                Text("All messages sorted by newest first")
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .padding(.top, 8)
                Spacer()
            } else {
                List {
                    ForEach(searchResults, id: \.message.id) { result in
                        Button(action: {
                            onNavigateToCategory(result.category)
                        }) {
                            VStack(alignment: .leading, spacing: 4) {
                                Text(result.message.mainMessage)
                                    .foregroundColor(.primary)
                                    .lineLimit(2)

                                HStack {
                                    Text("In: \(result.category.name)")
                                        .font(.caption)
                                        .foregroundColor(.secondary)

                                    Spacer()

                                    Text(result.message.relativeTimeString)
                                        .font(.caption)
                                        .foregroundColor(.secondary)
                                }
                            }
                            .padding(.vertical, 2)
                        }
                    }
                }
            }
        }
        .navigationTitle("Search")
        .onAppear {
            performSearch()
        }
    }
    
    private func performSearch() {
        var results: [(Message, Category)] = []

        // Function to collect all messages from a category and its subcategories
        func collectMessages(_ category: Category) {
            for message in category.messages {
                if searchText.isEmpty {
                    // Show all messages when no search text
                    results.append((message, category))
                } else if message.text.lowercased().contains(searchText.lowercased()) ||
                         message.mainMessage.lowercased().contains(searchText.lowercased()) {
                    // Search in both original text and parsed main message
                    results.append((message, category))
                }
            }

            for subcategory in category.subcategories {
                collectMessages(subcategory)
            }
        }

        // Collect messages from all root categories
        for rootCategory in categoryManager.rootCategories {
            collectMessages(rootCategory)
        }

        // Sort by timestamp in descending order (newest first)
        results.sort { $0.0.timestamp > $1.0.timestamp }

        searchResults = results
    }
}
