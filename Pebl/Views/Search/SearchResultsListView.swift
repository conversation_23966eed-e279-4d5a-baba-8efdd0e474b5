import SwiftUI

struct SearchResultsListView: View {
    let searchText: String
    @ObservedObject var categoryManager: CategoryManager
    let onSelectResult: (Category) -> Void
    
    @State private var searchResults: [(message: Message, category: Category)] = []
    
    var body: some View {
        VStack {
            if searchText.isEmpty {
                Text("Enter text to search")
                    .foregroundColor(.secondary)
                    .padding(.top, 40)
                Spacer()
            } else if searchResults.isEmpty {
                Text("No results found")
                    .foregroundColor(.secondary)
                    .padding(.top, 40)
                Spacer()
            } else {
                List {
                    ForEach(searchResults, id: \.message.id) { result in
                        Button(action: {
                            onSelectResult(result.category)
                        }) {
                            VStack(alignment: .leading, spacing: 4) {
                                Text(result.message.mainMessage)
                                    .foregroundColor(.primary)
                                    .lineLimit(2)
                                
                                HStack {
                                    Image(systemName: result.category.sfSymbol.isEmpty ? "folder.fill" : result.category.sfSymbol)
                                        .foregroundColor(.secondary)
                                        .font(.caption)
                                    
                                    Text(result.category.name)
                                        .font(.caption)
                                        .foregroundColor(.secondary)
                                }
                            }
                            .padding(.vertical, 4)
                        }
                    }
                }
                .listStyle(PlainListStyle())
            }
        }
        .onChange(of: searchText) { _ in
            performSearch()
        }
        .onAppear {
            performSearch()
        }
    }
    
    private func performSearch() {
        guard !searchText.isEmpty else {
            searchResults = []
            return
        }
        
        var results: [(Message, Category)] = []
        
        // Search through all categories recursively
        func searchCategory(_ category: Category) {
            for message in category.messages {
                if message.text.lowercased().contains(searchText.lowercased()) {
                    results.append((message, category))
                }
            }
            
            for subcategory in category.subcategories {
                searchCategory(subcategory)
            }
        }
        
        for rootCategory in categoryManager.rootCategories {
            searchCategory(rootCategory)
        }
        
        searchResults = results
    }
}