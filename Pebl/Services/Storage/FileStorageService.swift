//
//  FileStorageService.swift
//  Pebl
//
//  Created by AI Assistant on 7/2/25.
//

import Foundation

/// Protocol for file storage operations
protocol FileStorageServiceProtocol {
    func save<T: Codable>(_ object: T, to filename: String) throws
    func load<T: Codable>(_ type: T.Type, from filename: String) throws -> T
    func fileExists(_ filename: String) -> Bool
    func delete(_ filename: String) throws
    func getFileURL(for filename: String) -> URL?
}

/// Service for handling file storage operations
class FileStorageService: FileStorageServiceProtocol {
    
    // MARK: - Singleton
    
    static let shared = FileStorageService()
    
    private init() {}
    
    // MARK: - Private Properties
    
    private let fileManager = FileManager.default
    
    private var documentsDirectory: URL? {
        fileManager.urls(for: .documentDirectory, in: .userDomainMask).first
    }
    
    // MARK: - Public Methods
    
    /// Save a Codable object to a file
    func save<T: Codable>(_ object: T, to filename: String) throws {
        guard let fileURL = getFileURL(for: filename) else {
            throw StorageError.invalidFileURL
        }
        
        let encoder = JSONEncoder()
        encoder.outputFormatting = .prettyPrinted
        
        let data = try encoder.encode(object)
        try data.write(to: fileURL)
        
        if AppConfig.enableVerboseLogging {
            print("✅ Successfully saved \(filename)")
        }
    }
    
    /// Load a Codable object from a file
    func load<T: Codable>(_ type: T.Type, from filename: String) throws -> T {
        guard let fileURL = getFileURL(for: filename) else {
            throw StorageError.invalidFileURL
        }
        
        guard fileExists(filename) else {
            throw StorageError.fileNotFound
        }
        
        let data = try Data(contentsOf: fileURL)
        let decoder = JSONDecoder()
        let object = try decoder.decode(type, from: data)
        
        if AppConfig.enableVerboseLogging {
            print("✅ Successfully loaded \(filename)")
        }
        
        return object
    }
    
    /// Check if a file exists
    func fileExists(_ filename: String) -> Bool {
        guard let fileURL = getFileURL(for: filename) else {
            return false
        }
        return fileManager.fileExists(atPath: fileURL.path)
    }
    
    /// Delete a file
    func delete(_ filename: String) throws {
        guard let fileURL = getFileURL(for: filename) else {
            throw StorageError.invalidFileURL
        }
        
        guard fileExists(filename) else {
            throw StorageError.fileNotFound
        }
        
        try fileManager.removeItem(at: fileURL)
        
        if AppConfig.enableVerboseLogging {
            print("✅ Successfully deleted \(filename)")
        }
    }
    
    /// Get file URL for a given filename
    func getFileURL(for filename: String) -> URL? {
        return documentsDirectory?.appendingPathComponent(filename)
    }
    
    // MARK: - Backup Operations
    
    /// Create a backup of a file with timestamp
    func createBackup(of filename: String) throws -> String {
        guard fileExists(filename) else {
            throw StorageError.fileNotFound
        }
        
        let timestamp = DateFormatter.backupFormatter.string(from: Date())
        let backupFilename = "\(AppConfig.backupFilePrefix)\(timestamp)_\(filename)"
        
        guard let originalURL = getFileURL(for: filename),
              let backupURL = getFileURL(for: backupFilename) else {
            throw StorageError.invalidFileURL
        }
        
        try fileManager.copyItem(at: originalURL, to: backupURL)
        
        if AppConfig.enableVerboseLogging {
            print("✅ Created backup: \(backupFilename)")
        }
        
        return backupFilename
    }
    
    /// List all backup files
    func listBackups() -> [String] {
        guard let documentsDirectory = documentsDirectory else {
            return []
        }
        
        do {
            let files = try fileManager.contentsOfDirectory(atPath: documentsDirectory.path)
            return files.filter { $0.hasPrefix(AppConfig.backupFilePrefix) }
        } catch {
            print("Error listing backups: \(error)")
            return []
        }
    }
    
    /// Clean up old backup files (keep only the most recent N backups)
    func cleanupOldBackups(keepCount: Int = 5) {
        let backups = listBackups().sorted(by: >)
        let backupsToDelete = Array(backups.dropFirst(keepCount))
        
        for backup in backupsToDelete {
            do {
                try delete(backup)
            } catch {
                print("Error deleting backup \(backup): \(error)")
            }
        }
    }
}

// MARK: - Storage Errors

enum StorageError: LocalizedError {
    case invalidFileURL
    case fileNotFound
    case encodingFailed
    case decodingFailed
    
    var errorDescription: String? {
        switch self {
        case .invalidFileURL:
            return "Could not create file URL"
        case .fileNotFound:
            return "File not found"
        case .encodingFailed:
            return "Failed to encode data"
        case .decodingFailed:
            return "Failed to decode data"
        }
    }
}

// MARK: - Extensions

private extension DateFormatter {
    static let backupFormatter: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyyMMdd_HHmmss"
        return formatter
    }()
}
