//
//  SettingsStorageService.swift
//  Pebl
//
//  Created by AI Assistant on 7/2/25.
//

import Foundation

/// Service for handling settings storage operations
class SettingsStorageService {
    
    // MARK: - Singleton
    
    static let shared = SettingsStorageService()
    
    private init() {}
    
    // MARK: - Dependencies
    
    private let fileStorage = FileStorageService.shared
    
    // MARK: - Settings Storage
    
    /// Save app settings to persistent storage
    func saveSettings(_ settings: AppSettings) throws {
        try fileStorage.save(settings, to: AppConfig.settingsFileName)
    }
    
    /// Load app settings from persistent storage
    func loadSettings() throws -> AppSettings {
        return try fileStorage.load(AppSettings.self, from: AppConfig.settingsFileName)
    }
    
    /// Load settings with fallback to defaults
    func loadSettingsWithFallback() -> AppSettings {
        do {
            let settings = try loadSettings()
            if AppConfig.enableVerboseLogging {
                print("✅ Settings loaded from storage")
            }
            return settings
        } catch {
            if AppConfig.enableVerboseLogging {
                print("Settings not found or invalid, using defaults: \(error)")
            }
            return AppSettings()
        }
    }
    
    /// Check if settings file exists
    func settingsFileExists() -> Bool {
        return fileStorage.fileExists(AppConfig.settingsFileName)
    }
    
    /// Delete settings file (reset to defaults)
    func resetSettings() throws {
        if settingsFileExists() {
            try fileStorage.delete(AppConfig.settingsFileName)
        }
    }
    
    // MARK: - UserDefaults Integration
    
    /// Migrate settings from UserDefaults to file storage
    func migrateFromUserDefaults() -> AppSettings? {
        let userDefaults = UserDefaults.standard
        
        // Check if we have any legacy settings in UserDefaults
        let hasLegacySettings = userDefaults.object(forKey: "messageExpirationDays") != nil ||
                               userDefaults.object(forKey: "isDarkModeEnabled") != nil ||
                               userDefaults.object(forKey: "autoDeleteExpiredMessages") != nil
        
        guard hasLegacySettings else {
            return nil
        }
        
        // Create settings from UserDefaults
        var settings = AppSettings()
        
        if let expirationDays = userDefaults.object(forKey: "messageExpirationDays") as? Int {
            settings.messageExpirationDays = expirationDays
        }
        
        if let isDarkMode = userDefaults.object(forKey: "isDarkModeEnabled") as? Bool {
            settings.isDarkModeEnabled = isDarkMode
        }
        
        if let autoDelete = userDefaults.object(forKey: "autoDeleteExpiredMessages") as? Bool {
            settings.autoDeleteExpiredMessages = autoDelete
        }
        
        // Save to file storage
        do {
            try saveSettings(settings)
            
            // Clean up UserDefaults
            userDefaults.removeObject(forKey: "messageExpirationDays")
            userDefaults.removeObject(forKey: "isDarkModeEnabled")
            userDefaults.removeObject(forKey: "autoDeleteExpiredMessages")
            
            print("✅ Successfully migrated settings from UserDefaults")
            return settings
        } catch {
            print("Error migrating settings from UserDefaults: \(error)")
            return nil
        }
    }
    
    // MARK: - Validation
    
    /// Validate settings and fix any invalid values
    func validateAndFixSettings(_ settings: inout AppSettings) {
        // Validate message expiration days
        if settings.messageExpirationDays < 1 {
            settings.messageExpirationDays = AppConfig.defaultMessageExpirationDays
        } else if settings.messageExpirationDays > 365 {
            settings.messageExpirationDays = 365
        }
        
        // Other validation rules can be added here as needed
    }
    
    // MARK: - Export/Import
    
    /// Export settings to a shareable format
    func exportSettings(_ settings: AppSettings) throws -> Data {
        let encoder = JSONEncoder()
        encoder.outputFormatting = .prettyPrinted
        return try encoder.encode(settings)
    }
    
    /// Import settings from data
    func importSettings(from data: Data) throws -> AppSettings {
        let decoder = JSONDecoder()
        var settings = try decoder.decode(AppSettings.self, from: data)
        
        // Validate imported settings
        validateAndFixSettings(&settings)
        
        return settings
    }
    
    /// Export settings to file
    func exportSettingsToFile(_ settings: AppSettings, filename: String) throws {
        let data = try exportSettings(settings)
        guard let fileURL = fileStorage.getFileURL(for: filename) else {
            throw StorageError.invalidFileURL
        }
        try data.write(to: fileURL)
    }
    
    /// Import settings from file
    func importSettingsFromFile(_ filename: String) throws -> AppSettings {
        guard let fileURL = fileStorage.getFileURL(for: filename) else {
            throw StorageError.invalidFileURL
        }
        let data = try Data(contentsOf: fileURL)
        return try importSettings(from: data)
    }
}
