//
//  StorageCoordinator.swift
//  Pebl
//
//  Created by AI Assistant on 7/2/25.
//

import Foundation

/// Coordinates all storage operations across the app
class StorageCoordinator {
    
    // MARK: - Singleton
    
    static let shared = StorageCoordinator()
    
    private init() {}
    
    // MARK: - Dependencies
    
    private let fileStorage = FileStorageService.shared
    private let categoryStorage = CategoryStorageService.shared
    private let settingsStorage = SettingsStorageService.shared
    
    // MARK: - Initialization
    
    /// Initialize storage system and perform any necessary migrations
    func initializeStorage() {
        if AppConfig.enableVerboseLogging {
            print("🔧 Initializing storage system...")
        }
        
        // Perform any necessary migrations
        performMigrations()
        
        // Clean up old files
        performMaintenanceTasks()
        
        if AppConfig.enableVerboseLogging {
            print("✅ Storage system initialized")
        }
    }
    
    // MARK: - Data Management
    
    /// Save all app data
    func saveAllData(categoryManager: CategoryManager, settingsManager: SettingsManager) {
        do {
            try categoryStorage.saveCategories(from: categoryManager)
            try settingsStorage.saveSettings(settingsManager.settings)
            
            if AppConfig.enableVerboseLogging {
                print("✅ All data saved successfully")
            }
        } catch {
            print("❌ Error saving app data: \(error)")
        }
    }
    
    /// Load all app data
    func loadAllData(into categoryManager: CategoryManager, and settingsManager: SettingsManager) {
        // Load categories
        categoryStorage.loadCategoriesWithFallback(into: categoryManager)
        
        // Load settings
        settingsManager.settings = settingsStorage.loadSettingsWithFallback()
        
        if AppConfig.enableVerboseLogging {
            print("✅ All data loaded successfully")
        }
    }
    
    // MARK: - Backup & Restore
    
    /// Create a complete backup of all app data
    func createCompleteBackup() throws -> [String] {
        var backupFiles: [String] = []
        
        // Backup categories
        if categoryStorage.categoriesFileExists() {
            let categoryBackup = try fileStorage.createBackup(of: AppConfig.categoriesFileName)
            backupFiles.append(categoryBackup)
        }
        
        // Backup settings
        if settingsStorage.settingsFileExists() {
            let settingsBackup = try fileStorage.createBackup(of: AppConfig.settingsFileName)
            backupFiles.append(settingsBackup)
        }
        
        if AppConfig.enableVerboseLogging {
            print("✅ Complete backup created: \(backupFiles)")
        }
        
        return backupFiles
    }
    
    /// Get storage statistics
    func getStorageStatistics() -> StorageStatistics {
        let backups = fileStorage.listBackups()
        
        return StorageStatistics(
            categoriesFileExists: categoryStorage.categoriesFileExists(),
            settingsFileExists: settingsStorage.settingsFileExists(),
            backupCount: backups.count,
            backupFiles: backups
        )
    }
    
    // MARK: - Maintenance
    
    /// Perform regular maintenance tasks
    func performMaintenanceTasks() {
        // Clean up old backups
        fileStorage.cleanupOldBackups(keepCount: AppConfig.maxBackupFiles)
        
        if AppConfig.enableVerboseLogging {
            print("🧹 Storage maintenance completed")
        }
    }
    
    /// Reset all app data (for testing or user request)
    func resetAllData() throws {
        if categoryStorage.categoriesFileExists() {
            try categoryStorage.deleteCategoriesFile()
        }
        
        if settingsStorage.settingsFileExists() {
            try settingsStorage.resetSettings()
        }
        
        if AppConfig.enableVerboseLogging {
            print("🔄 All app data reset")
        }
    }
    
    // MARK: - Export/Import
    
    /// Export all app data to a single file
    func exportAllData(categoryManager: CategoryManager, settingsManager: SettingsManager) throws -> Data {
        let exportData = AppDataExport(
            categories: categoryManager.rootCategories,
            settings: settingsManager.settings,
            exportDate: Date(),
            appVersion: AppConfig.appVersion
        )
        
        let encoder = JSONEncoder()
        encoder.outputFormatting = .prettyPrinted
        encoder.dateEncodingStrategy = .iso8601
        
        return try encoder.encode(exportData)
    }
    
    /// Import all app data from a file
    func importAllData(from data: Data, into categoryManager: CategoryManager, and settingsManager: SettingsManager) throws {
        let decoder = JSONDecoder()
        decoder.dateDecodingStrategy = .iso8601
        
        let importData = try decoder.decode(AppDataExport.self, from: data)
        
        // Validate import data
        guard !importData.categories.isEmpty else {
            throw StorageError.decodingFailed
        }
        
        // Set up category manager references
        for category in importData.categories {
            category.categoryManager = categoryManager
            setCategoryManagerRecursively(for: category, manager: categoryManager)
        }
        
        // Update managers
        categoryManager.rootCategories = importData.categories
        settingsManager.settings = importData.settings
        
        // Save imported data
        try saveAllData(categoryManager: categoryManager, settingsManager: settingsManager)
        
        if AppConfig.enableVerboseLogging {
            print("✅ Data imported successfully from \(importData.exportDate)")
        }
    }
    
    // MARK: - Private Methods
    
    private func performMigrations() {
        // Migrate settings from UserDefaults if needed
        _ = settingsStorage.migrateFromUserDefaults()
        
        // Other migrations can be added here
    }
    
    private func setCategoryManagerRecursively(for category: Category, manager: CategoryManager) {
        category.categoryManager = manager
        for subcategory in category.subcategories {
            setCategoryManagerRecursively(for: subcategory, manager: manager)
        }
    }
}

// MARK: - Supporting Types

/// Statistics about storage usage
struct StorageStatistics {
    let categoriesFileExists: Bool
    let settingsFileExists: Bool
    let backupCount: Int
    let backupFiles: [String]
}

/// Complete app data export structure
private struct AppDataExport: Codable {
    let categories: [Category]
    let settings: AppSettings
    let exportDate: Date
    let appVersion: String
}
