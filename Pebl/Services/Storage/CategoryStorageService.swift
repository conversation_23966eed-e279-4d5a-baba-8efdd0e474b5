//
//  CategoryStorageService.swift
//  Pebl
//
//  Created by AI Assistant on 7/2/25.
//

import Foundation

/// Service for handling category-specific storage operations
class CategoryStorageService {
    
    // MARK: - Singleton
    
    static let shared = CategoryStorageService()
    
    private init() {}
    
    // MARK: - Dependencies
    
    private let fileStorage = FileStorageService.shared
    
    // MARK: - Category Storage
    
    /// Save categories to persistent storage
    func saveCategories(_ categories: [Category]) throws {
        // Create backup before saving
        if fileStorage.fileExists(AppConfig.categoriesFileName) {
            do {
                _ = try fileStorage.createBackup(of: AppConfig.categoriesFileName)
            } catch {
                print("Warning: Could not create backup before saving categories: \(error)")
            }
        }
        
        try fileStorage.save(categories, to: AppConfig.categoriesFileName)
        
        // Clean up old backups
        fileStorage.cleanupOldBackups()
    }
    
    /// Load categories from persistent storage
    func loadCategories() throws -> [Category] {
        return try fileStorage.load([Category].self, from: AppConfig.categoriesFileName)
    }
    
    /// Check if categories file exists
    func categoriesFileExists() -> Bool {
        return fileStorage.fileExists(AppConfig.categoriesFileName)
    }
    
    /// Delete categories file
    func deleteCategoriesFile() throws {
        try fileStorage.delete(AppConfig.categoriesFileName)
    }
    
    // MARK: - Category Manager Integration
    
    /// Save categories with proper manager references
    func saveCategories(from categoryManager: CategoryManager) throws {
        try saveCategories(categoryManager.rootCategories)
    }
    
    /// Load categories and set up manager references
    func loadCategories(into categoryManager: CategoryManager) throws {
        let categories = try loadCategories()
        
        // Set category manager references
        for category in categories {
            category.categoryManager = categoryManager
            setCategoryManagerRecursively(for: category, manager: categoryManager)
        }
        
        categoryManager.rootCategories = categories
    }
    
    /// Load categories with fallback to defaults
    func loadCategoriesWithFallback(into categoryManager: CategoryManager) {
        do {
            try loadCategories(into: categoryManager)
            if AppConfig.enableVerboseLogging {
                print("✅ Categories loaded from storage")
            }
        } catch {
            print("Error loading categories: \(error)")
            print("Loading default categories instead")
            categoryManager.loadDefaultCategories()
        }
    }
    
    // MARK: - Migration Support
    
    /// Migrate from legacy storage format if needed
    func migrateFromLegacyFormat(into categoryManager: CategoryManager) -> Bool {
        // Check if we need to migrate from old format
        guard let fileURL = fileStorage.getFileURL(for: AppConfig.categoriesFileName),
              let data = try? Data(contentsOf: fileURL) else {
            return false
        }
        
        // Try to decode as legacy format first
        if let legacyCategories = try? JSONDecoder().decode([LegacyCategory].self, from: data) {
            // Convert to new format
            let convertedCategories = legacyCategories.map { convertLegacyCategory($0) }
            
            // Set up manager references
            for category in convertedCategories {
                category.categoryManager = categoryManager
                setCategoryManagerRecursively(for: category, manager: categoryManager)
            }
            
            categoryManager.rootCategories = convertedCategories
            
            // Save in new format
            do {
                try saveCategories(from: categoryManager)
                print("✅ Successfully migrated categories from legacy format")
                return true
            } catch {
                print("Error saving migrated categories: \(error)")
                return false
            }
        }
        
        return false
    }
    
    // MARK: - Private Helpers
    
    private func setCategoryManagerRecursively(for category: Category, manager: CategoryManager) {
        category.categoryManager = manager
        for subcategory in category.subcategories {
            setCategoryManagerRecursively(for: subcategory, manager: manager)
        }
    }
    
    private func convertLegacyCategory(_ legacy: LegacyCategory) -> Category {
        let category = Category(
            name: legacy.name,
            sfSymbol: legacy.sfSymbol,
            parent: nil
        )
        
        // Convert messages
        category.messages = legacy.messages.map { legacyMessage in
            Message(
                text: legacyMessage.text,
                isCompleted: legacyMessage.isCompleted,
                categoryName: category.name,
                timestamp: legacyMessage.timestamp ?? Date()
            )
        }
        
        // Convert subcategories recursively
        category.subcategories = legacy.subcategories.map { legacySubcategory in
            let subcategory = convertLegacyCategory(legacySubcategory)
            subcategory.parent = category
            return subcategory
        }
        
        return category
    }
}

// MARK: - Legacy Support

/// Legacy category structure for migration
private struct LegacyCategory: Codable {
    let name: String
    let sfSymbol: String?
    let messages: [LegacyMessage]
    let subcategories: [LegacyCategory]
}

/// Legacy message structure for migration
private struct LegacyMessage: Codable {
    let text: String
    let isCompleted: Bool
    let timestamp: Date?
}
