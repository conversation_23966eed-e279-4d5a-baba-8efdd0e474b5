//
//  CategorizationEngine.swift
//  Pebl
//
//  Created by AI Assistant on 6/22/25.
//

import Foundation

/// Centralized categorization engine that handles all categorization logic
/// This is the single source of truth for categorization decisions
class CategorizationEngine {
    
    // MARK: - Configuration
    
    struct CategorizationConfig {
        let enableBatchRecategorization: Bool
        let enableSmartSubfolderCreation: Bool
        let enableNewCategoryCreation: Bool
        let maxBatchSize: Int
        let recategorizationThreshold: Int // Minimum messages before triggering batch recategorization
        
        static let `default` = CategorizationConfig(
            enableBatchRecategorization: true,
            enableSmartSubfolderCreation: true,
            enableNewCategoryCreation: true,
            maxBatchSize: 50,
            recategorizationThreshold: 5
        )
    }
    
    // MARK: - Properties
    
    private let aiModel: AIModel
    private let messageParser: MessageParser
    private let config: CategorizationConfig
    private weak var categoryManager: CategoryManager?
    
    // MARK: - Initialization
    
    init(aiModel: AIModel = AIModel(), 
         messageParser: MessageParser = MessageParser(), 
         config: CategorizationConfig = .default,
         categoryManager: CategoryManager? = nil) {
        self.aiModel = aiModel
        self.messageParser = messageParser
        self.config = config
        self.categoryManager = categoryManager
    }
    
    // MARK: - Main Categorization Interface
    
    /// Single entry point for all categorization decisions
    func categorizeMessage(_ message: String, 
                          availableCategories: [String],
                          completion: @escaping (CategorizationResult) -> Void) {
        
        DispatchQueue.global(qos: .userInitiated).async {
            let result = self.performCategorization(message, availableCategories: availableCategories)
            
            DispatchQueue.main.async {
                completion(result)
            }
        }
    }
    
    /// Batch categorization for multiple messages
    func batchCategorizeMessages(_ messages: [String],
                                availableCategories: [String],
                                progress: @escaping (Int, Int) -> Void,
                                completion: @escaping ([CategorizationResult]) -> Void) {
        
        DispatchQueue.global(qos: .userInitiated).async {
            var results: [CategorizationResult] = []
            
            for (index, message) in messages.enumerated() {
                let result = self.performCategorization(message, availableCategories: availableCategories)
                results.append(result)
                
                DispatchQueue.main.async {
                    progress(index + 1, messages.count)
                }
            }
            
            DispatchQueue.main.async {
                completion(results)
            }
        }
    }
    
    /// Re-categorize messages when a new subcategory is added
    func recategorizeAfterSubcategoryAddition(newSubcategory: Category,
                                             parentCategory: Category,
                                             completion: @escaping (RecategorizationResult) -> Void) {
        
        guard config.enableBatchRecategorization else {
            completion(RecategorizationResult(movedMessages: [], skippedCount: 0, newCategoriesCreated: []))
            return
        }
        
        DispatchQueue.global(qos: .userInitiated).async {
            let result = self.performRecategorization(newSubcategory: newSubcategory, parentCategory: parentCategory)
            
            DispatchQueue.main.async {
                completion(result)
            }
        }
    }
    
    // MARK: - Core Categorization Logic
    
    private func performCategorization(_ message: String, availableCategories: [String]) -> CategorizationResult {
        
        // Step 1: Parse the message for better understanding
        let parsedMessage = MessageParser.parseMessage(message, for: "Generic")
        
        // Step 2: Get AI categorization
        let aiCategoryName = aiModel.categorizeMessage(message, availableCategories: availableCategories)
        
        // Step 3: Determine if we should create a new category or use existing
        let decision = makeCategorizationDecision(
            originalMessage: message,
            parsedMessage: parsedMessage,
            aiSuggestion: aiCategoryName,
            availableCategories: availableCategories
        )
        
        // Step 4: Check if we should create a subfolder
        var subfolderRecommendation: SubfolderRecommendation?
        if let existingCategory = findCategory(named: decision.categoryName, in: availableCategories) {
            subfolderRecommendation = evaluateSubfolderCreation(
                message: message,
                targetCategory: existingCategory
            )
        }
        
        return CategorizationResult(
            originalMessage: message,
            parsedMessage: parsedMessage,
            categoryName: decision.categoryName,
            isNewCategory: decision.isNewCategory,
            confidence: decision.confidence,
            subfolderRecommendation: subfolderRecommendation,
            reasoning: decision.reasoning
        )
    }
    
    private func makeCategorizationDecision(originalMessage: String,
                                          parsedMessage: MessageParser.ParsedMessage,
                                          aiSuggestion: String,
                                          availableCategories: [String]) -> CategorizationDecision {

        // Check if AI suggestion is valid
        if availableCategories.contains(aiSuggestion) {
            return CategorizationDecision(
                categoryName: aiSuggestion,
                isNewCategory: false,
                confidence: 0.9,
                reasoning: "AI matched existing category"
            )
        }

        // Check for partial matches (e.g., "Action Movies" when AI suggests "Movies")
        let partialMatch = findPartialCategoryMatch(aiSuggestion, in: availableCategories, for: originalMessage)
        if let match = partialMatch {
            return CategorizationDecision(
                categoryName: match.categoryName,
                isNewCategory: false,
                confidence: match.confidence,
                reasoning: "Partial match: \(match.reasoning)"
            )
        }

        // Check if AI suggestion is a reasonable new category
        if config.enableNewCategoryCreation && isValidNewCategory(aiSuggestion, for: originalMessage) {
            return CategorizationDecision(
                categoryName: aiSuggestion,
                isNewCategory: true,
                confidence: 0.8,
                reasoning: "AI suggested valid new category"
            )
        }

        // Enhanced pattern matching for subcategory scenarios
        let patternMatch = findBestCategoryByPatternEnhanced(originalMessage, availableCategories: availableCategories)
        return CategorizationDecision(
            categoryName: patternMatch.categoryName,
            isNewCategory: false,
            confidence: patternMatch.confidence,
            reasoning: patternMatch.reasoning
        )
    }
    
    private func performRecategorization(newSubcategory: Category, parentCategory: Category) -> RecategorizationResult {
        var movedMessages: [MessageMove] = []
        var skippedCount = 0
        var newCategoriesCreated: [String] = []

        // Get all messages from parent and sibling subcategories
        let allMessages = collectMessagesForRecategorization(parentCategory: parentCategory)

        print("🔄 Recategorization: Found \(allMessages.count) messages to evaluate")

        // Always proceed if we have any messages (remove threshold check for subcategory addition)
        guard !allMessages.isEmpty else {
            print("⚠️ No messages found for recategorization")
            return RecategorizationResult(movedMessages: [], skippedCount: 0, newCategoriesCreated: [])
        }

        // Get comprehensive category list including the new subcategory and all available categories
        let availableCategories = getComprehensiveCategoryNames(parentCategory: parentCategory)
        print("📁 Available categories for recategorization: \(availableCategories)")

        // Process messages in batches
        let batches = allMessages.chunked(into: config.maxBatchSize)

        for batch in batches {
            for messageInfo in batch {
                let result = performCategorization(messageInfo.message.text, availableCategories: availableCategories)

                print("📝 Message: '\(messageInfo.message.text)' → '\(result.categoryName)' (was: '\(messageInfo.currentCategoryName)')")

                // Check if message should move to a different category
                if result.categoryName != messageInfo.currentCategoryName {
                    let move = MessageMove(
                        message: messageInfo.message,
                        fromCategory: messageInfo.currentCategoryName,
                        toCategory: result.categoryName,
                        confidence: result.confidence
                    )
                    movedMessages.append(move)

                    print("✅ Will move: '\(messageInfo.message.text)' from '\(messageInfo.currentCategoryName)' to '\(result.categoryName)'")

                    // Track new categories created during recategorization
                    if result.isNewCategory && !newCategoriesCreated.contains(result.categoryName) {
                        newCategoriesCreated.append(result.categoryName)
                        print("🆕 New category will be created: '\(result.categoryName)'")
                    }
                } else {
                    skippedCount += 1
                    print("⏭️ Staying: '\(messageInfo.message.text)' remains in '\(messageInfo.currentCategoryName)'")
                }
            }
        }

        print("📊 Recategorization summary: \(movedMessages.count) moves, \(skippedCount) stayed, \(newCategoriesCreated.count) new categories")

        return RecategorizationResult(
            movedMessages: movedMessages,
            skippedCount: skippedCount,
            newCategoriesCreated: newCategoriesCreated
        )
    }
    
    // MARK: - Helper Functions
    
    private func isValidNewCategory(_ categoryName: String, for message: String) -> Bool {
        // Use existing validation logic from AIModel
        return categoryName.count >= 3 && 
               categoryName.count <= 30 && 
               !categoryName.contains("The provided") &&
               !categoryName.contains("I cannot")
    }
    
    private func findPartialCategoryMatch(_ aiSuggestion: String, in availableCategories: [String], for message: String) -> (categoryName: String, confidence: Double, reasoning: String)? {
        let suggestionLower = aiSuggestion.lowercased()
        let messageLower = message.lowercased()

        // Look for categories that contain the AI suggestion or vice versa
        for category in availableCategories {
            let categoryLower = category.lowercased()

            // Check if category contains the suggestion (e.g., "Action Movies" contains "Movies")
            if categoryLower.contains(suggestionLower) || suggestionLower.contains(categoryLower) {

                // Additional validation for movie-related messages
                if (suggestionLower.contains("movie") || suggestionLower.contains("film")) &&
                   (messageLower.contains("watch") || messageLower.contains("movie") || messageLower.contains("film")) {
                    return (category, 0.85, "Movie content matched to movie subcategory")
                }

                // Additional validation for shopping-related messages
                if suggestionLower.contains("shop") &&
                   (messageLower.contains("buy") || messageLower.contains("purchase") || messageLower.contains("order")) {
                    return (category, 0.85, "Shopping content matched to shopping subcategory")
                }

                // General partial match
                return (category, 0.75, "Partial category name match")
            }
        }

        return nil
    }

    private func findBestCategoryByPattern(_ message: String, availableCategories: [String]) -> String {
        let result = findBestCategoryByPatternEnhanced(message, availableCategories: availableCategories)
        return result.categoryName
    }

    private func findBestCategoryByPatternEnhanced(_ message: String, availableCategories: [String]) -> (categoryName: String, confidence: Double, reasoning: String) {
        // Enhanced pattern-based fallback logic
        let messageLower = message.lowercased()

        // Movie patterns - look for movie subcategories first
        if messageLower.contains("watch") || messageLower.contains("movie") || messageLower.contains("film") {
            // Look for specific movie subcategories first
            let movieSubcategories = availableCategories.filter { category in
                let categoryLower = category.lowercased()
                return categoryLower.contains("movie") || categoryLower.contains("film") ||
                       categoryLower.contains("action") || categoryLower.contains("comedy") ||
                       categoryLower.contains("drama") || categoryLower.contains("horror")
            }

            if !movieSubcategories.isEmpty {
                // Try to match specific genres
                if messageLower.contains("action") || messageLower.contains("fight") || messageLower.contains("war") {
                    if let actionCategory = movieSubcategories.first(where: { $0.lowercased().contains("action") }) {
                        return (actionCategory, 0.9, "Action movie pattern match")
                    }
                }

                if messageLower.contains("comedy") || messageLower.contains("funny") || messageLower.contains("laugh") {
                    if let comedyCategory = movieSubcategories.first(where: { $0.lowercased().contains("comedy") }) {
                        return (comedyCategory, 0.9, "Comedy movie pattern match")
                    }
                }

                // Default to first movie subcategory
                return (movieSubcategories.first!, 0.8, "Movie subcategory match")
            }

            // Fallback to general movie categories
            let match = findBestMatch(["Movies to Watch", "Entertainment", "To-Do"], in: availableCategories)
            return (match, 0.7, "General movie pattern match")
        }

        // Shopping patterns
        if messageLower.contains("buy") || messageLower.contains("purchase") || messageLower.contains("order") {
            let match = findBestMatch(["Shopping", "To-Do"], in: availableCategories)
            return (match, 0.8, "Shopping pattern match")
        }

        // Reading patterns
        if messageLower.contains("read") || messageLower.contains("book") {
            let match = findBestMatch(["To-Read", "Books", "To-Do"], in: availableCategories)
            return (match, 0.8, "Reading pattern match")
        }

        // Default fallback
        let match = findBestMatch(["To-Do", "Uncategorized"], in: availableCategories)
        return (match, 0.5, "Default fallback")
    }
    
    private func findBestMatch(_ preferences: [String], in availableCategories: [String]) -> String {
        for preference in preferences {
            if availableCategories.contains(preference) {
                return preference
            }
        }
        return availableCategories.first ?? "Uncategorized"
    }
    
    private func findCategory(named name: String, in availableCategories: [String]) -> Category? {
        return categoryManager?.findCategory(named: name)
    }
    
    private func evaluateSubfolderCreation(message: String, targetCategory: Category) -> SubfolderRecommendation? {
        guard config.enableSmartSubfolderCreation else { return nil }
        
        let subfolderResult = aiModel.shouldCreateSubfolder(for: message, in: targetCategory)
        
        if subfolderResult.shouldCreate, let subfolderName = subfolderResult.subfolderName {
            return SubfolderRecommendation(
                subfolderName: subfolderName,
                confidence: 0.8,
                reasoning: "AI recommended subfolder creation"
            )
        }
        
        return nil
    }
    
    private func collectMessagesForRecategorization(parentCategory: Category) -> [MessageInfo] {
        var allMessages: [MessageInfo] = []
        
        // Add messages from parent category
        for message in parentCategory.messages {
            allMessages.append(MessageInfo(message: message, currentCategoryName: parentCategory.name))
        }
        
        // Add messages from all subcategories
        for subcategory in parentCategory.subcategories {
            for message in subcategory.messages {
                allMessages.append(MessageInfo(message: message, currentCategoryName: subcategory.name))
            }
        }
        
        return allMessages
    }
    
    private func getAllCategoryNames(from parentCategory: Category) -> [String] {
        var names = [parentCategory.name]
        for subcategory in parentCategory.subcategories {
            names.append(subcategory.name)
        }
        return names
    }

    private func getComprehensiveCategoryNames(parentCategory: Category) -> [String] {
        // Get all categories from the category manager for comprehensive recategorization
        if let categoryManager = categoryManager {
            return categoryManager.getAllCategoryNames()
        } else {
            // Fallback to just parent and subcategories
            return getAllCategoryNames(from: parentCategory)
        }
    }
}

// MARK: - Supporting Types

struct CategorizationResult {
    let originalMessage: String
    let parsedMessage: MessageParser.ParsedMessage
    let categoryName: String
    let isNewCategory: Bool
    let confidence: Double
    let subfolderRecommendation: SubfolderRecommendation?
    let reasoning: String
}

struct CategorizationDecision {
    let categoryName: String
    let isNewCategory: Bool
    let confidence: Double
    let reasoning: String
}

struct SubfolderRecommendation {
    let subfolderName: String
    let confidence: Double
    let reasoning: String
}

struct RecategorizationResult {
    let movedMessages: [MessageMove]
    let skippedCount: Int
    let newCategoriesCreated: [String]
}

struct MessageMove {
    let message: Message
    let fromCategory: String
    let toCategory: String
    let confidence: Double
}

struct MessageInfo {
    let message: Message
    let currentCategoryName: String
}

// MARK: - Array Extension for Chunking

extension Array {
    func chunked(into size: Int) -> [[Element]] {
        return stride(from: 0, to: count, by: size).map {
            Array(self[$0..<Swift.min($0 + size, count)])
        }
    }
}
