//
//  HashtagParser.swift
//  Pebl
//
//  Created by AI Assistant on 6/30/25.
//

import Foundation

/// Parses hashtag-based category specifications from messages
class HashtagParser {

    // MARK: - Category Name Normalization

    /// Normalize a category name for fuzzy matching
    /// Converts to lowercase, removes special characters, and normalizes spacing
    static func normalizeCategoryName(_ name: String) -> String {
        return name
            .lowercased()
            .replacingOccurrences(of: "-", with: "")
            .replacingOccurrences(of: "_", with: "")
            .replacingOccurrences(of: " ", with: "")
            .trimmingCharacters(in: .whitespacesAndNewlines)
    }

    /// Find the best matching existing category name using fuzzy matching
    /// - Parameters:
    ///   - targetName: The category name from hashtag
    ///   - existingCategories: List of existing category names
    /// - Returns: The best matching existing category name, or nil if no good match
    static func findBestMatchingCategory(_ targetName: String, from existingCategories: [String]) -> String? {
        let normalizedTarget = normalizeCategoryName(targetName)

        // First try exact normalized match
        for existing in existingCategories {
            if normalizeCategoryName(existing) == normalizedTarget {
                return existing
            }
        }

        // If no exact match, try partial matching for common variations
        for existing in existingCategories {
            let normalizedExisting = normalizeCategoryName(existing)

            // Check if one is contained in the other (for cases like "todo" vs "to-do" or "movies" vs "moviestowatch")
            if normalizedTarget.contains(normalizedExisting) || normalizedExisting.contains(normalizedTarget) {
                // For containment matches, be more lenient with length differences
                // Allow matches where the shorter string is at least 3 characters and represents a meaningful portion
                let shorterLength = min(normalizedTarget.count, normalizedExisting.count)
                let longerLength = max(normalizedTarget.count, normalizedExisting.count)

                // Allow match if:
                // 1. Shorter string is at least 3 characters (avoid very short matches)
                // 2. Shorter string is at least 40% of the longer string (meaningful portion)
                if shorterLength >= 3 && Double(shorterLength) / Double(longerLength) >= 0.4 {
                    return existing
                }
            }
        }

        return nil
    }
    
    /// Result of parsing a message for hashtag category specifications
    struct HashtagParseResult {
        let cleanedMessage: String
        let categoryPath: CategoryPath?
        let originalMessage: String
        
        init(cleanedMessage: String, categoryPath: CategoryPath?, originalMessage: String) {
            self.cleanedMessage = cleanedMessage
            self.categoryPath = categoryPath
            self.originalMessage = originalMessage
        }
    }
    
    /// Represents a category path (category and optional subcategory)
    struct CategoryPath {
        let category: String
        let subcategory: String?
        
        init(category: String, subcategory: String? = nil) {
            self.category = category
            self.subcategory = subcategory
        }
        
        /// Returns true if this represents a subcategory specification
        var hasSubcategory: Bool {
            return subcategory != nil
        }
        
        /// Returns the full path as a string for debugging
        var fullPath: String {
            if let subcategory = subcategory {
                return "\(category)/\(subcategory)"
            }
            return category
        }
    }
    
    // MARK: - Public Interface
    
    /// Parse a message for hashtag category specifications
    /// - Parameter message: The original message text
    /// - Returns: HashtagParseResult containing cleaned message and category path if found
    static func parseMessage(_ message: String) -> HashtagParseResult {
        let trimmedMessage = message.trimmingCharacters(in: .whitespacesAndNewlines)

        // Check for hashtag at the beginning
        if let result = parseHashtagAtBeginning(trimmedMessage, originalMessage: message) {
            return result
        }

        // Check for hashtag at the end
        if let result = parseHashtagAtEnd(trimmedMessage, originalMessage: message) {
            return result
        }

        // No hashtag found
        return HashtagParseResult(
            cleanedMessage: trimmedMessage,
            categoryPath: nil,
            originalMessage: message
        )
    }
    
    // MARK: - Private Parsing Methods
    
    /// Parse hashtag at the beginning of the message
    private static func parseHashtagAtBeginning(_ message: String, originalMessage: String) -> HashtagParseResult? {
        // Pattern: #category or #category/subcategory followed by space and message
        // Hashtag names don't contain spaces - use hyphens or underscores instead
        let pattern = #"^#([a-zA-Z0-9\-_]+(?:/[a-zA-Z0-9\-_]+)?)\s+(.+)$"#

        guard let regex = try? NSRegularExpression(pattern: pattern, options: []),
              let match = regex.firstMatch(in: message, options: [], range: NSRange(location: 0, length: message.count)) else {
            return nil
        }

        let categoryPathString = String(message[Range(match.range(at: 1), in: message)!])
        let remainingMessage = String(message[Range(match.range(at: 2), in: message)!])

        guard let categoryPath = parseCategoryPath(categoryPathString) else {
            return nil
        }

        return HashtagParseResult(
            cleanedMessage: remainingMessage.trimmingCharacters(in: .whitespacesAndNewlines),
            categoryPath: categoryPath,
            originalMessage: originalMessage
        )
    }
    
    /// Parse hashtag at the end of the message
    private static func parseHashtagAtEnd(_ message: String, originalMessage: String) -> HashtagParseResult? {
        // Pattern: message followed by space and #category or #category/subcategory
        // Hashtag names don't contain spaces - use hyphens or underscores instead
        let pattern = #"^(.+)\s+#([a-zA-Z0-9\-_]+(?:/[a-zA-Z0-9\-_]+)?)$"#

        guard let regex = try? NSRegularExpression(pattern: pattern, options: []),
              let match = regex.firstMatch(in: message, options: [], range: NSRange(location: 0, length: message.count)) else {
            return nil
        }

        let remainingMessage = String(message[Range(match.range(at: 1), in: message)!])
        let categoryPathString = String(message[Range(match.range(at: 2), in: message)!])

        guard let categoryPath = parseCategoryPath(categoryPathString) else {
            return nil
        }

        return HashtagParseResult(
            cleanedMessage: remainingMessage.trimmingCharacters(in: .whitespacesAndNewlines),
            categoryPath: categoryPath,
            originalMessage: originalMessage
        )
    }
    
    /// Parse category path string into CategoryPath object
    private static func parseCategoryPath(_ pathString: String) -> CategoryPath? {
        let trimmedPath = pathString.trimmingCharacters(in: .whitespacesAndNewlines)

        // Check if it's empty or invalid
        guard !trimmedPath.isEmpty else {
            return nil
        }

        // Check if the entire path string is an error pattern (before splitting)
        let lowercased = trimmedPath.lowercased()
        let invalidPatterns = ["error", "unknown", "null", "undefined", "none", "n/a"]
        guard !invalidPatterns.contains(lowercased) else {
            return nil
        }

        // Split by forward slash for subcategory
        let components = trimmedPath.components(separatedBy: "/")

        if components.count == 1 {
            // Just a category
            let category = components[0].trimmingCharacters(in: .whitespacesAndNewlines)
            guard !category.isEmpty && isValidCategoryName(category) else {
                return nil
            }
            return CategoryPath(category: category)
        } else if components.count == 2 {
            // Category and subcategory
            let category = components[0].trimmingCharacters(in: .whitespacesAndNewlines)
            let subcategory = components[1].trimmingCharacters(in: .whitespacesAndNewlines)

            guard !category.isEmpty && !subcategory.isEmpty &&
                  isValidCategoryName(category) && isValidCategoryName(subcategory) else {
                return nil
            }

            return CategoryPath(category: category, subcategory: subcategory)
        } else {
            // Too many slashes - invalid
            return nil
        }
    }
    
    /// Validate if a category name is valid
    private static func isValidCategoryName(_ name: String) -> Bool {
        // Category names should be reasonable length and contain valid characters
        let trimmed = name.trimmingCharacters(in: .whitespacesAndNewlines)
        
        // Check length (1-50 characters)
        guard trimmed.count >= 1 && trimmed.count <= 50 else {
            return false
        }
        
        // Check for valid characters (letters, numbers, spaces, basic punctuation)
        let allowedCharacterSet = CharacterSet.alphanumerics.union(.whitespaces).union(CharacterSet(charactersIn: "-_"))
        guard trimmed.rangeOfCharacter(from: allowedCharacterSet.inverted) == nil else {
            return false
        }
        
        // Avoid common error patterns
        let lowercased = trimmed.lowercased()
        let invalidPatterns = ["error", "unknown", "null", "undefined", "none", "n/a"]
        
        return !invalidPatterns.contains(lowercased)
    }
}
