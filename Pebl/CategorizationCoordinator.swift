//
//  CategorizationCoordinator.swift
//  Pebl
//
//  Created by AI Assistant on 6/22/25.
//

import Foundation

/// Coordinates categorization operations between the UI and the categorization engine
/// This is the main interface that other parts of the app should use
class CategorizationCoordinator: ObservableObject {
    
    // MARK: - Properties
    
    @Published var isProcessing = false
    @Published var batchProgress: BatchProgress?
    
    private let engine: CategorizationEngine
    private weak var categoryManager: CategoryManager?
    
    // MARK: - Initialization
    
    init(categoryManager: CategoryManager) {
        self.categoryManager = categoryManager
        self.engine = CategorizationEngine(categoryManager: categoryManager)
    }
    
    // MARK: - Public Interface
    
    /// Add a single message with automatic categorization
    func addMessage(_ messageText: String, completion: @escaping (Bool) -> Void) {
        guard let categoryManager = categoryManager else {
            completion(false)
            return
        }

        isProcessing = true

        // First, check if the message contains hashtag category specification
        let hashtagResult = HashtagParser.parseMessage(messageText)

        if let categoryPath = hashtagResult.categoryPath {
            // Handle hashtag-specified categorization
            handleHashtagCategorization(
                cleanedMessage: hashtagResult.cleanedMessage,
                categoryPath: categoryPath,
                originalMessage: hashtagResult.originalMessage,
                completion: completion
            )
        } else {
            // Fall back to AI categorization
            let availableCategories = categoryManager.getAllCategoryNames()

            engine.categorizeMessage(messageText, availableCategories: availableCategories) { [weak self] result in
                self?.handleCategorizationResult(result, completion: completion)
            }
        }
    }
    
    /// Add a new subcategory and trigger recategorization
    func addSubcategory(name: String, 
                       sfSymbol: String = "folder.fill", 
                       to parentCategory: Category,
                       completion: @escaping (SubcategoryAdditionResult) -> Void) {
        
        isProcessing = true
        
        // First, create the subcategory
        let newSubcategory = parentCategory.addSubcategory(name: name, sfSymbol: sfSymbol)
        
        // Then trigger recategorization
        engine.recategorizeAfterSubcategoryAddition(
            newSubcategory: newSubcategory,
            parentCategory: parentCategory
        ) { [weak self] recategorizationResult in
            
            // Apply the recategorization results
            self?.applyRecategorizationResults(recategorizationResult) { success in
                let result = SubcategoryAdditionResult(
                    subcategory: newSubcategory,
                    recategorization: recategorizationResult,
                    success: success
                )
                
                self?.isProcessing = false
                completion(result)
            }
        }
    }
    
    /// Manually trigger recategorization for a category
    func recategorizeCategory(_ category: Category, completion: @escaping (RecategorizationResult) -> Void) {
        isProcessing = true
        
        // Create a temporary "new" subcategory to trigger recategorization logic
        // This is a bit of a hack, but it reuses the existing logic
        let tempSubcategory = Category(name: "temp", sfSymbol: "folder.fill", parent: category)
        
        engine.recategorizeAfterSubcategoryAddition(
            newSubcategory: tempSubcategory,
            parentCategory: category
        ) { [weak self] result in
            
            // Remove the temporary subcategory
            category.removeSubcategory(tempSubcategory)
            
            self?.applyRecategorizationResults(result) { _ in
                self?.isProcessing = false
                completion(result)
            }
        }
    }
    
    /// Batch add multiple messages
    func batchAddMessages(_ messages: [String], completion: @escaping (BatchAddResult) -> Void) {
        guard let categoryManager = categoryManager else {
            completion(BatchAddResult(successCount: 0, failureCount: messages.count, results: []))
            return
        }
        
        isProcessing = true
        batchProgress = BatchProgress(current: 0, total: messages.count)
        
        let availableCategories = categoryManager.getAllCategoryNames()
        
        engine.batchCategorizeMessages(
            messages,
            availableCategories: availableCategories,
            progress: { [weak self] current, total in
                self?.batchProgress = BatchProgress(current: current, total: total)
            },
            completion: { [weak self] results in
                self?.handleBatchCategorizationResults(results, completion: completion)
            }
        )
    }
    
    // MARK: - Private Implementation
    
    private func handleCategorizationResult(_ result: CategorizationResult, completion: @escaping (Bool) -> Void) {
        guard let categoryManager = categoryManager else {
            isProcessing = false
            completion(false)
            return
        }
        
        // Find or create the target category
        var targetCategory: Category?
        
        if result.isNewCategory {
            // Create new root category
            DispatchQueue.global(qos: .userInitiated).async {
                let aiModel = AIModel()
                let sfSymbol = aiModel.getSFSymbolForCategory(result.categoryName)
                
                DispatchQueue.main.async {
                    targetCategory = categoryManager.addRootCategory(name: result.categoryName, sfSymbol: sfSymbol)
                    self.addMessageToCategory(result, targetCategory: targetCategory!, completion: completion)
                }
            }
        } else {
            // Use existing category
            targetCategory = categoryManager.findCategory(named: result.categoryName)
            addMessageToCategory(result, targetCategory: targetCategory!, completion: completion)
        }
    }
    
    private func addMessageToCategory(_ result: CategorizationResult, targetCategory: Category, completion: @escaping (Bool) -> Void) {

        // Check if we should create a subfolder
        if let subfolderRec = result.subfolderRecommendation {
            // Create subfolder with recategorization and add message there
            DispatchQueue.global(qos: .userInitiated).async {
                let aiModel = AIModel()
                let sfSymbol = aiModel.getSFSymbolForCategory(subfolderRec.subfolderName)

                DispatchQueue.main.async {
                    // Use the recategorization-enabled method for AI-created subcategories
                    self.addSubcategory(
                        name: subfolderRec.subfolderName,
                        sfSymbol: sfSymbol,
                        to: targetCategory
                    ) { [weak self] subcategoryResult in
                        // Add the message to the newly created subcategory
                        subcategoryResult.subcategory.addMessage(result.originalMessage)

                        self?.isProcessing = false
                        completion(subcategoryResult.success)
                    }
                }
            }
        } else {
            // Add directly to target category
            targetCategory.addMessage(result.originalMessage)

            isProcessing = false
            completion(true)
        }
    }
    
    private func handleBatchCategorizationResults(_ results: [CategorizationResult], completion: @escaping (BatchAddResult) -> Void) {
        var successCount = 0
        var failureCount = 0
        let totalResults = results.count
        
        let dispatchGroup = DispatchGroup()
        
        for result in results {
            dispatchGroup.enter()
            
            handleCategorizationResult(result) { success in
                if success {
                    successCount += 1
                } else {
                    failureCount += 1
                }
                dispatchGroup.leave()
            }
        }
        
        dispatchGroup.notify(queue: .main) {
            self.isProcessing = false
            self.batchProgress = nil
            
            let batchResult = BatchAddResult(
                successCount: successCount,
                failureCount: failureCount,
                results: results
            )
            completion(batchResult)
        }
    }
    
    private func applyRecategorizationResults(_ result: RecategorizationResult, completion: @escaping (Bool) -> Void) {
        guard let categoryManager = categoryManager else {
            print("❌ No category manager available for applying recategorization")
            completion(false)
            return
        }

        print("🔄 Applying recategorization: \(result.movedMessages.count) messages to move")

        guard !result.movedMessages.isEmpty else {
            print("ℹ️ No messages to move")
            completion(true)
            return
        }

        let dispatchGroup = DispatchGroup()
        var overallSuccess = true
        var moveCount = 0

        // Move messages to their new categories
        for move in result.movedMessages {
            dispatchGroup.enter()

            print("📦 Moving message '\(move.message.text)' from '\(move.fromCategory)' to '\(move.toCategory)'")

            // Remove from old category
            if let oldCategory = categoryManager.findCategory(named: move.fromCategory) {
                oldCategory.removeMessage(withId: move.message.id)
                print("✅ Removed from '\(move.fromCategory)'")
            } else {
                print("⚠️ Could not find old category '\(move.fromCategory)'")
            }

            // Add to new category (create if needed)
            var targetCategory = categoryManager.findCategory(named: move.toCategory)

            if targetCategory == nil {
                print("🆕 Creating new category '\(move.toCategory)'")
                // Create new category
                DispatchQueue.global(qos: .userInitiated).async {
                    let aiModel = AIModel()
                    let sfSymbol = aiModel.getSFSymbolForCategory(move.toCategory)

                    DispatchQueue.main.async {
                        targetCategory = categoryManager.addRootCategory(name: move.toCategory, sfSymbol: sfSymbol)
                        targetCategory?.addMessage(move.message)
                        moveCount += 1
                        print("✅ Added to new category '\(move.toCategory)'")
                        dispatchGroup.leave()
                    }
                }
            } else {
                targetCategory?.addMessage(move.message)
                moveCount += 1
                print("✅ Added to existing category '\(move.toCategory)'")
                dispatchGroup.leave()
            }
        }

        dispatchGroup.notify(queue: .main) {
            print("🎉 Recategorization complete: \(moveCount) messages moved successfully")
            completion(overallSuccess)
        }
    }

    // MARK: - Hashtag Categorization

    /// Handle categorization when a hashtag is specified in the message
    private func handleHashtagCategorization(cleanedMessage: String,
                                           categoryPath: HashtagParser.CategoryPath,
                                           originalMessage: String,
                                           completion: @escaping (Bool) -> Void) {
        guard let categoryManager = categoryManager else {
            isProcessing = false
            completion(false)
            return
        }

        print("📝 Processing hashtag categorization: #\(categoryPath.fullPath)")

        // Try to find existing category using fuzzy matching
        let existingCategoryNames = categoryManager.getAllCategoryNames()
        let bestMatch = HashtagParser.findBestMatchingCategory(categoryPath.category, from: existingCategoryNames)

        var targetCategory: Category?
        if let matchedName = bestMatch {
            targetCategory = categoryManager.findCategory(named: matchedName)
            print("🔍 Fuzzy matched hashtag '#\(categoryPath.category)' to existing category '\(matchedName)'")
        } else {
            // No existing match found, will create new category
            targetCategory = nil
            print("🆕 No match found for '#\(categoryPath.category)', will create new category")
        }

        if targetCategory == nil {
            print("🆕 Creating new category '\(categoryPath.category)'")
            // Create new category with AI-generated SF Symbol
            DispatchQueue.global(qos: .userInitiated).async { [weak self] in
                let aiModel = AIModel()
                let sfSymbol = aiModel.getSFSymbolForCategory(categoryPath.category)

                DispatchQueue.main.async {
                    targetCategory = categoryManager.addRootCategory(name: categoryPath.category, sfSymbol: sfSymbol)
                    self?.addMessageToTarget(
                        cleanedMessage: cleanedMessage,
                        originalMessage: originalMessage,
                        targetCategory: targetCategory!,
                        subcategoryName: categoryPath.subcategory,
                        completion: completion
                    )
                }
            }
        } else {
            // Category exists, proceed with adding message
            addMessageToTarget(
                cleanedMessage: cleanedMessage,
                originalMessage: originalMessage,
                targetCategory: targetCategory!,
                subcategoryName: categoryPath.subcategory,
                completion: completion
            )
        }
    }

    /// Add message to the target category, creating subcategory if needed
    private func addMessageToTarget(cleanedMessage: String,
                                  originalMessage: String,
                                  targetCategory: Category,
                                  subcategoryName: String?,
                                  completion: @escaping (Bool) -> Void) {

        if let subcategoryName = subcategoryName {
            // Need to add to subcategory - try fuzzy matching first
            let existingSubcategoryNames = targetCategory.subcategories.map { $0.name }
            let bestSubMatch = HashtagParser.findBestMatchingCategory(subcategoryName, from: existingSubcategoryNames)

            var targetSubcategory: Category?
            if let matchedSubName = bestSubMatch {
                targetSubcategory = targetCategory.findSubcategory(named: matchedSubName)
                print("🔍 Fuzzy matched subcategory '#\(subcategoryName)' to existing '\(matchedSubName)'")
            } else {
                targetSubcategory = nil
                print("🆕 No subcategory match found for '#\(subcategoryName)', will create new")
            }

            if targetSubcategory == nil {
                print("🆕 Creating new subcategory '\(subcategoryName)' in '\(targetCategory.name)'")
                // Create new subcategory with AI-generated SF Symbol
                DispatchQueue.global(qos: .userInitiated).async { [weak self] in
                    let aiModel = AIModel()
                    let sfSymbol = aiModel.getSFSymbolForCategory(subcategoryName)

                    DispatchQueue.main.async {
                        let newSubcategory = targetCategory.addSubcategory(name: subcategoryName, sfSymbol: sfSymbol)
                        newSubcategory.addMessage(cleanedMessage)

                        self?.isProcessing = false
                        print("✅ Added message to new subcategory '\(subcategoryName)'")
                        completion(true)
                    }
                }
            } else {
                // Subcategory exists, add message directly
                targetSubcategory!.addMessage(cleanedMessage)
                isProcessing = false
                print("✅ Added message to existing subcategory '\(targetSubcategory!.name)'")
                completion(true)
            }
        } else {
            // Add to main category
            targetCategory.addMessage(cleanedMessage)
            isProcessing = false
            print("✅ Added message to category '\(targetCategory.name)'")
            completion(true)
        }
    }
}

// MARK: - Supporting Types

struct BatchProgress {
    let current: Int
    let total: Int
    
    var percentage: Double {
        guard total > 0 else { return 0 }
        return Double(current) / Double(total)
    }
}

struct SubcategoryAdditionResult {
    let subcategory: Category
    let recategorization: RecategorizationResult
    let success: Bool
}

struct BatchAddResult {
    let successCount: Int
    let failureCount: Int
    let results: [CategorizationResult]
    
    var totalCount: Int {
        return successCount + failureCount
    }
    
    var successRate: Double {
        guard totalCount > 0 else { return 0 }
        return Double(successCount) / Double(totalCount)
    }
}
