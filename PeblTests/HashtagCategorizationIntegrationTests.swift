//
//  HashtagCategorizationIntegrationTests.swift
//  PeblTests
//
//  Created by AI Assistant on 6/30/25.
//

import XCTest
@testable import Pebl

class HashtagCategorizationIntegrationTests: XCTestCase {
    
    var categoryManager: CategoryManager!
    
    override func setUp() {
        super.setUp()
        categoryManager = CategoryManager()
    }
    
    override func tearDown() {
        categoryManager = nil
        super.tearDown()
    }
    
    func testHashtagCategorizationToExistingCategory() {
        let expectation = XCTestExpectation(description: "Message categorized with hashtag")
        
        // Add message with hashtag to existing category
        categoryManager.addMessage("#Shopping Buy milk") { success in
            XCTAssertTrue(success)
            
            // Verify message was added to Shopping category
            let shoppingCategory = self.categoryManager.findCategory(named: "Shopping")
            XCTAssertNotNil(shoppingCategory)
            XCTAssertEqual(shoppingCategory?.messages.count, 1)
            XCTAssertEqual(shoppingCategory?.messages.first?.text, "Buy milk")
            
            expectation.fulfill()
        }
        
        wait(for: [expectation], timeout: 5.0)
    }
    
    func testHashtagCategorizationToNewCategory() {
        let expectation = XCTestExpectation(description: "Message categorized with hashtag to new category")
        
        // Add message with hashtag to new category
        categoryManager.addMessage("#Books Read 1984") { success in
            XCTAssertTrue(success)
            
            // Verify new category was created
            let booksCategory = self.categoryManager.findCategory(named: "Books")
            XCTAssertNotNil(booksCategory)
            XCTAssertEqual(booksCategory?.messages.count, 1)
            XCTAssertEqual(booksCategory?.messages.first?.text, "Read 1984")
            
            expectation.fulfill()
        }
        
        wait(for: [expectation], timeout: 5.0)
    }
    
    func testHashtagCategorizationToNewSubcategory() {
        let expectation = XCTestExpectation(description: "Message categorized with hashtag to new subcategory")
        
        // Add message with hashtag to new subcategory
        categoryManager.addMessage("#Movies to Watch/Action Watch John Wick") { success in
            XCTAssertTrue(success)
            
            // Verify category and subcategory were created
            let moviesCategory = self.categoryManager.findCategory(named: "Movies to Watch")
            XCTAssertNotNil(moviesCategory)
            
            let actionSubcategory = moviesCategory?.findSubcategory(named: "Action")
            XCTAssertNotNil(actionSubcategory)
            XCTAssertEqual(actionSubcategory?.messages.count, 1)
            XCTAssertEqual(actionSubcategory?.messages.first?.text, "Watch John Wick")
            
            expectation.fulfill()
        }
        
        wait(for: [expectation], timeout: 5.0)
    }
    
    func testHashtagCategorizationToExistingSubcategory() {
        let expectation = XCTestExpectation(description: "Message categorized with hashtag to existing subcategory")
        
        // First create a category with subcategory
        let moviesCategory = categoryManager.addRootCategory(name: "Movies", sfSymbol: "film")
        let actionSubcategory = moviesCategory.addSubcategory(name: "Action", sfSymbol: "bolt")
        
        // Add message with hashtag to existing subcategory
        categoryManager.addMessage("#Movies/Action Watch Die Hard") { success in
            XCTAssertTrue(success)
            
            // Verify message was added to existing subcategory
            XCTAssertEqual(actionSubcategory.messages.count, 1)
            XCTAssertEqual(actionSubcategory.messages.first?.text, "Watch Die Hard")
            
            expectation.fulfill()
        }
        
        wait(for: [expectation], timeout: 5.0)
    }
    
    func testFallbackToAIWhenNoHashtag() {
        let expectation = XCTestExpectation(description: "Message categorized with AI when no hashtag")
        
        // Add message without hashtag - should fall back to AI categorization
        categoryManager.addMessage("Watch The Matrix") { success in
            XCTAssertTrue(success)
            
            // Verify message was categorized (should go to Movies to Watch based on AI)
            let moviesCategory = self.categoryManager.findCategory(named: "Movies to Watch")
            XCTAssertNotNil(moviesCategory)
            XCTAssertGreaterThan(moviesCategory?.messages.count ?? 0, 0)
            
            expectation.fulfill()
        }
        
        wait(for: [expectation], timeout: 5.0)
    }
    
    func testHashtagCategorizationWithMultiWordCategories() {
        let expectation = XCTestExpectation(description: "Message categorized with multi-word hashtag")
        
        // Test with multi-word category and subcategory
        categoryManager.addMessage("#Home Improvement/Kitchen Remodel Install new cabinets") { success in
            XCTAssertTrue(success)
            
            // Verify category and subcategory were created with correct names
            let homeCategory = self.categoryManager.findCategory(named: "Home Improvement")
            XCTAssertNotNil(homeCategory)
            
            let kitchenSubcategory = homeCategory?.findSubcategory(named: "Kitchen Remodel")
            XCTAssertNotNil(kitchenSubcategory)
            XCTAssertEqual(kitchenSubcategory?.messages.count, 1)
            XCTAssertEqual(kitchenSubcategory?.messages.first?.text, "Install new cabinets")
            
            expectation.fulfill()
        }
        
        wait(for: [expectation], timeout: 5.0)
    }
    
    func testHashtagAtEndOfMessage() {
        let expectation = XCTestExpectation(description: "Message with hashtag at end")
        
        // Test hashtag at the end of message
        categoryManager.addMessage("Buy organic vegetables #Grocery Shopping") { success in
            XCTAssertTrue(success)
            
            // Verify message was categorized correctly
            let groceryCategory = self.categoryManager.findCategory(named: "Grocery Shopping")
            XCTAssertNotNil(groceryCategory)
            XCTAssertEqual(groceryCategory?.messages.count, 1)
            XCTAssertEqual(groceryCategory?.messages.first?.text, "Buy organic vegetables")
            
            expectation.fulfill()
        }
        
        wait(for: [expectation], timeout: 5.0)
    }
    
    func testInvalidHashtagFallsBackToAI() {
        let expectation = XCTestExpectation(description: "Invalid hashtag falls back to AI")
        
        // Test with invalid hashtag - should fall back to AI categorization
        categoryManager.addMessage("#error Watch a movie") { success in
            XCTAssertTrue(success)
            
            // Should have fallen back to AI and categorized as movies
            let moviesCategory = self.categoryManager.findCategory(named: "Movies to Watch")
            XCTAssertNotNil(moviesCategory)
            
            // The message should contain the original text including the invalid hashtag
            let hasMovieMessage = moviesCategory?.messages.contains { message in
                message.text.contains("error") && message.text.contains("Watch a movie")
            } ?? false
            XCTAssertTrue(hasMovieMessage)
            
            expectation.fulfill()
        }
        
        wait(for: [expectation], timeout: 5.0)
    }

    // MARK: - Fuzzy Matching Tests

    func testFuzzyMatchingToExistingCategory() {
        let expectation = XCTestExpectation(description: "Fuzzy matching to existing category")

        // Test various hashtag formats that should match existing "To-Read" category
        categoryManager.addMessage("#toread Read The Hobbit") { success in
            XCTAssertTrue(success)

            // Should match to existing "To-Read" category
            let toReadCategory = self.categoryManager.findCategory(named: "To-Read")
            XCTAssertNotNil(toReadCategory)

            // Check that message was added to the existing category
            let hasMessage = toReadCategory?.messages.contains { $0.text == "Read The Hobbit" } ?? false
            XCTAssertTrue(hasMessage, "Message should be in To-Read category")

            expectation.fulfill()
        }

        wait(for: [expectation], timeout: 5.0)
    }

    func testFuzzyMatchingWithSpecialCharacters() {
        let expectation = XCTestExpectation(description: "Fuzzy matching with special characters")

        // Test hashtag with underscores matching existing "To-Do" category
        categoryManager.addMessage("#to_do Complete project") { success in
            XCTAssertTrue(success)

            // Should match to existing "To-Do" category
            let toDoCategory = self.categoryManager.findCategory(named: "To-Do")
            XCTAssertNotNil(toDoCategory)

            // Check that message was added to the existing category
            let hasMessage = toDoCategory?.messages.contains { $0.text == "Complete project" } ?? false
            XCTAssertTrue(hasMessage, "Message should be in To-Do category")

            expectation.fulfill()
        }

        wait(for: [expectation], timeout: 5.0)
    }

    func testFuzzyMatchingCaseInsensitive() {
        let expectation = XCTestExpectation(description: "Case insensitive fuzzy matching")

        // Test uppercase hashtag matching existing "Shopping" category
        categoryManager.addMessage("#SHOPPING Buy groceries") { success in
            XCTAssertTrue(success)

            // Should match to existing "Shopping" category
            let shoppingCategory = self.categoryManager.findCategory(named: "Shopping")
            XCTAssertNotNil(shoppingCategory)

            // Check that message was added to the existing category
            let hasMessage = shoppingCategory?.messages.contains { $0.text == "Buy groceries" } ?? false
            XCTAssertTrue(hasMessage, "Message should be in Shopping category")

            expectation.fulfill()
        }

        wait(for: [expectation], timeout: 5.0)
    }

    func testFuzzyMatchingPartialMatch() {
        let expectation = XCTestExpectation(description: "Partial fuzzy matching")

        // Test partial match to "Movies to Watch" category
        categoryManager.addMessage("#movies Watch Inception") { success in
            XCTAssertTrue(success)

            // Should match to existing "Movies to Watch" category
            let moviesCategory = self.categoryManager.findCategory(named: "Movies to Watch")
            XCTAssertNotNil(moviesCategory)

            // Check that message was added to the existing category
            let hasMessage = moviesCategory?.messages.contains { $0.text == "Watch Inception" } ?? false
            XCTAssertTrue(hasMessage, "Message should be in Movies to Watch category")

            expectation.fulfill()
        }

        wait(for: [expectation], timeout: 5.0)
    }

    func testFuzzyMatchingSubcategories() {
        let expectation = XCTestExpectation(description: "Fuzzy matching for subcategories")

        // First create a category with a subcategory
        let moviesCategory = categoryManager.addRootCategory(name: "Movies", sfSymbol: "film")
        let actionSubcategory = moviesCategory.addSubcategory(name: "Action-Movies", sfSymbol: "bolt")

        // Test fuzzy matching to existing subcategory
        categoryManager.addMessage("#Movies/actionmovies Watch John Wick") { success in
            XCTAssertTrue(success)

            // Should match to existing "Action-Movies" subcategory
            XCTAssertEqual(actionSubcategory.messages.count, 1)
            XCTAssertEqual(actionSubcategory.messages.first?.text, "Watch John Wick")

            expectation.fulfill()
        }

        wait(for: [expectation], timeout: 5.0)
    }

    func testNoFuzzyMatchCreatesNewCategory() {
        let expectation = XCTestExpectation(description: "No fuzzy match creates new category")

        // Test with a hashtag that shouldn't match any existing category
        categoryManager.addMessage("#completely_different_category New message") { success in
            XCTAssertTrue(success)

            // Should create a new category
            let newCategory = self.categoryManager.findCategory(named: "completely_different_category")
            XCTAssertNotNil(newCategory)
            XCTAssertEqual(newCategory?.messages.count, 1)
            XCTAssertEqual(newCategory?.messages.first?.text, "New message")

            expectation.fulfill()
        }

        wait(for: [expectation], timeout: 5.0)
    }
}
