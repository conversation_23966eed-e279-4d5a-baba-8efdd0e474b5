//
//  HashtagParserTests.swift
//  PeblTests
//
//  Created by AI Assistant on 6/30/25.
//

import XCTest
@testable import Pebl

class HashtagParserTests: XCTestCase {
    
    // MARK: - Basic Hashtag Parsing Tests
    
    func testHashtagAtBeginning() {
        let result = HashtagParser.parseMessage("#movies Watch The Dark Knight")

        // Just test the basic functionality first
        XCTAssertNotNil(result.categoryPath, "Category path should not be nil for '#movies Watch The Dark Knight'")

        if result.categoryPath == nil {
            // If it's nil, let's test a simpler case
            let simpleResult = HashtagParser.parseMessage("#test hello")
            XCTAssertNotNil(simpleResult.categoryPath, "Category path should not be nil for '#test hello'")
        }

        XCTAssertEqual(result.categoryPath?.category, "movies", "Category should be 'movies'")
        XCTAssertNil(result.categoryPath?.subcategory, "Subcategory should be nil")
        XCTAssertEqual(result.cleanedMessage, "Watch The Dark Knight", "Cleaned message should be 'Watch The Dark Knight'")
        XCTAssertEqual(result.originalMessage, "#movies Watch The Dark Knight", "Original message should be preserved")
    }
    
    func testHashtagAtEnd() {
        let result = HashtagParser.parseMessage("Watch The Dark Knight #movies")
        
        XCTAssertNotNil(result.categoryPath)
        XCTAssertEqual(result.categoryPath?.category, "movies")
        XCTAssertNil(result.categoryPath?.subcategory)
        XCTAssertEqual(result.cleanedMessage, "Watch The Dark Knight")
        XCTAssertEqual(result.originalMessage, "Watch The Dark Knight #movies")
    }
    
    func testHashtagWithSubcategoryAtBeginning() {
        let result = HashtagParser.parseMessage("#movies/action Watch John Wick")
        
        XCTAssertNotNil(result.categoryPath)
        XCTAssertEqual(result.categoryPath?.category, "movies")
        XCTAssertEqual(result.categoryPath?.subcategory, "action")
        XCTAssertEqual(result.cleanedMessage, "Watch John Wick")
        XCTAssertTrue(result.categoryPath?.hasSubcategory == true)
        XCTAssertEqual(result.categoryPath?.fullPath, "movies/action")
    }
    
    func testHashtagWithSubcategoryAtEnd() {
        let result = HashtagParser.parseMessage("Watch John Wick #movies/action")
        
        XCTAssertNotNil(result.categoryPath)
        XCTAssertEqual(result.categoryPath?.category, "movies")
        XCTAssertEqual(result.categoryPath?.subcategory, "action")
        XCTAssertEqual(result.cleanedMessage, "Watch John Wick")
    }
    
    // MARK: - Multi-word Category Tests
    
    func testMultiWordCategory() {
        let result = HashtagParser.parseMessage("#to-do Complete the project")

        XCTAssertNotNil(result.categoryPath)
        XCTAssertEqual(result.categoryPath?.category, "to-do")
        XCTAssertNil(result.categoryPath?.subcategory)
        XCTAssertEqual(result.cleanedMessage, "Complete the project")
    }
    
    func testMultiWordCategoryAndSubcategory() {
        let result = HashtagParser.parseMessage("Buy groceries #shopping-list/weekly-items")

        XCTAssertNotNil(result.categoryPath)
        XCTAssertEqual(result.categoryPath?.category, "shopping-list")
        XCTAssertEqual(result.categoryPath?.subcategory, "weekly-items")
        XCTAssertEqual(result.cleanedMessage, "Buy groceries")
    }
    
    // MARK: - No Hashtag Tests
    
    func testNoHashtag() {
        let result = HashtagParser.parseMessage("Just a regular message")
        
        XCTAssertNil(result.categoryPath)
        XCTAssertEqual(result.cleanedMessage, "Just a regular message")
        XCTAssertEqual(result.originalMessage, "Just a regular message")
    }
    
    func testHashtagInMiddle() {
        // Hashtags in the middle should not be parsed as category specifications
        let result = HashtagParser.parseMessage("This is a #hashtag in the middle of text")
        
        XCTAssertNil(result.categoryPath)
        XCTAssertEqual(result.cleanedMessage, "This is a #hashtag in the middle of text")
    }
    
    // MARK: - Invalid Hashtag Tests
    
    func testEmptyHashtag() {
        let result = HashtagParser.parseMessage("# Watch a movie")
        
        XCTAssertNil(result.categoryPath)
        XCTAssertEqual(result.cleanedMessage, "# Watch a movie")
    }
    
    func testHashtagWithOnlySlash() {
        let result = HashtagParser.parseMessage("#/ Watch a movie")
        
        XCTAssertNil(result.categoryPath)
        XCTAssertEqual(result.cleanedMessage, "#/ Watch a movie")
    }
    
    func testHashtagWithEmptySubcategory() {
        let result = HashtagParser.parseMessage("#movies/ Watch a movie")
        
        XCTAssertNil(result.categoryPath)
        XCTAssertEqual(result.cleanedMessage, "#movies/ Watch a movie")
    }
    
    func testHashtagWithTooManySlashes() {
        let result = HashtagParser.parseMessage("#movies/action/thriller Watch a movie")
        
        XCTAssertNil(result.categoryPath)
        XCTAssertEqual(result.cleanedMessage, "#movies/action/thriller Watch a movie")
    }
    
    // MARK: - Edge Cases
    
    func testHashtagWithSpecialCharacters() {
        let result = HashtagParser.parseMessage("#movies@action Watch a movie")
        
        XCTAssertNil(result.categoryPath)
        XCTAssertEqual(result.cleanedMessage, "#movies@action Watch a movie")
    }
    
    func testHashtagWithNumbers() {
        let result = HashtagParser.parseMessage("#movies2024 Watch a movie")
        
        XCTAssertNotNil(result.categoryPath)
        XCTAssertEqual(result.categoryPath?.category, "movies2024")
        XCTAssertEqual(result.cleanedMessage, "Watch a movie")
    }
    
    func testHashtagWithHyphensAndUnderscores() {
        let result = HashtagParser.parseMessage("#to-do_list Complete task")
        
        XCTAssertNotNil(result.categoryPath)
        XCTAssertEqual(result.categoryPath?.category, "to-do_list")
        XCTAssertEqual(result.cleanedMessage, "Complete task")
    }
    
    func testVeryLongCategoryName() {
        let longName = String(repeating: "a", count: 60)
        let result = HashtagParser.parseMessage("#\(longName) Test message")
        
        // Should be rejected due to length
        XCTAssertNil(result.categoryPath)
    }
    
    func testErrorPatternRejection() {
        let errorPatterns = ["error", "unknown", "null", "undefined", "none", "n/a"]
        
        for pattern in errorPatterns {
            let result = HashtagParser.parseMessage("#\(pattern) Test message")
            XCTAssertNil(result.categoryPath, "Should reject error pattern: \(pattern)")
        }
    }
    
    // MARK: - Whitespace Handling
    
    func testHashtagWithExtraWhitespace() {
        let result = HashtagParser.parseMessage("#  movies   /  action   Watch a movie")
        
        XCTAssertNil(result.categoryPath) // Should fail due to extra spaces in pattern
    }
    
    func testTrimmedInput() {
        let result = HashtagParser.parseMessage("  #movies Watch a movie  ")
        
        XCTAssertNotNil(result.categoryPath)
        XCTAssertEqual(result.categoryPath?.category, "movies")
        XCTAssertEqual(result.cleanedMessage, "Watch a movie")
    }
    
    // MARK: - CategoryPath Tests

    func testCategoryPathProperties() {
        let categoryOnly = HashtagParser.CategoryPath(category: "movies")
        XCTAssertFalse(categoryOnly.hasSubcategory)
        XCTAssertEqual(categoryOnly.fullPath, "movies")

        let categoryWithSub = HashtagParser.CategoryPath(category: "movies", subcategory: "action")
        XCTAssertTrue(categoryWithSub.hasSubcategory)
        XCTAssertEqual(categoryWithSub.fullPath, "movies/action")
    }

    // MARK: - Category Name Normalization Tests

    func testCategoryNameNormalization() {
        // Test basic normalization
        XCTAssertEqual(HashtagParser.normalizeCategoryName("To-Read"), "toread")
        XCTAssertEqual(HashtagParser.normalizeCategoryName("To_Do"), "todo")
        XCTAssertEqual(HashtagParser.normalizeCategoryName("Shopping List"), "shoppinglist")
        XCTAssertEqual(HashtagParser.normalizeCategoryName("MOVIES"), "movies")

        // Test with mixed cases and special characters
        XCTAssertEqual(HashtagParser.normalizeCategoryName("To-Do_List"), "todolist")
        XCTAssertEqual(HashtagParser.normalizeCategoryName("  Home-Improvement  "), "homeimprovement")
    }

    func testFuzzyMatching() {
        let existingCategories = ["To-Read", "To-Do", "Shopping", "Movies to Watch", "Home Improvement"]

        // Test exact normalized matches
        XCTAssertEqual(HashtagParser.findBestMatchingCategory("toread", from: existingCategories), "To-Read")
        XCTAssertEqual(HashtagParser.findBestMatchingCategory("todo", from: existingCategories), "To-Do")
        XCTAssertEqual(HashtagParser.findBestMatchingCategory("shopping", from: existingCategories), "Shopping")

        // Test case variations
        XCTAssertEqual(HashtagParser.findBestMatchingCategory("TOREAD", from: existingCategories), "To-Read")
        XCTAssertEqual(HashtagParser.findBestMatchingCategory("ToDo", from: existingCategories), "To-Do")

        // Test with special characters
        XCTAssertEqual(HashtagParser.findBestMatchingCategory("to-read", from: existingCategories), "To-Read")
        XCTAssertEqual(HashtagParser.findBestMatchingCategory("to_do", from: existingCategories), "To-Do")
        XCTAssertEqual(HashtagParser.findBestMatchingCategory("home_improvement", from: existingCategories), "Home Improvement")

        // Test partial matches
        XCTAssertEqual(HashtagParser.findBestMatchingCategory("movies", from: existingCategories), "Movies to Watch")
        XCTAssertEqual(HashtagParser.findBestMatchingCategory("moviestowatch", from: existingCategories), "Movies to Watch")

        // Test no match
        XCTAssertNil(HashtagParser.findBestMatchingCategory("completely_different", from: existingCategories))
        XCTAssertNil(HashtagParser.findBestMatchingCategory("verylongcategorynamethatdoesntmatch", from: existingCategories))
    }

    func testFuzzyMatchingEdgeCases() {
        let existingCategories = ["Books", "Book Reviews", "Bookmarks"]

        // Should match "Books" not "Book Reviews" or "Bookmarks"
        XCTAssertEqual(HashtagParser.findBestMatchingCategory("books", from: existingCategories), "Books")

        // Should not match if too different
        XCTAssertNil(HashtagParser.findBestMatchingCategory("bo", from: existingCategories))

        // Test empty arrays
        XCTAssertNil(HashtagParser.findBestMatchingCategory("anything", from: []))
    }
}
