# Pebl

A smart personal assistant iOS app that helps you organize your thoughts and tasks (pebls) into meaningful categories using intelligent AI categorization.

## Features

### Core Functionality
- **Smart AI Categorization**: Automatically categorizes messages using advanced AI
- **Dynamic Category Creation**: Creates new categories when appropriate
- **Hashtag Support**: Use #category or #category/subcategory for manual categorization
- **Message Completion**: Toggle messages between active and completed states
- **Subcategory Management**: Automatic subcategory creation when categories reach threshold
- **URL Content Analysis**: Extracts meaningful content from URLs for better organization

### Default Categories
- To-Read
- Shopping
- To-Do
- Movies to Watch
- Appointments

### Modern UI Features
- Clean SwiftUI interface with modern design patterns
- Square checkboxes for completion states
- Main/subcontent message display
- Collapsible subcategory views
- Pull-to-clear completed items
- Three-dot menu for advanced actions

## Architecture

This app follows **L6 Engineering Standards** with clean, modular architecture:

### Directory Structure
```
Pebl/
├── App/
│   ├── Configuration/     # App configuration and settings
│   └── PeblApp.swift     # Main app entry point
├── Models/               # Data models and structures
│   ├── Category.swift
│   ├── Message.swift
│   └── CategoryManager.swift
├── Views/                # SwiftUI views organized by feature
│   ├── Main/            # Main app views
│   ├── Category/        # Category-related views
│   ├── Message/         # Message-related views
│   └── Components/      # Reusable UI components
├── Services/            # Business logic and external services
│   ├── AI/             # AI categorization services
│   ├── Storage/        # Data persistence services
│   └── Categorization/ # Category management services
└── Utilities/          # Helper functions and utilities
    ├── Parsers/        # Message and hashtag parsing
    └── Extensions/     # Swift extensions

Tests/
└── UnitTests/          # Comprehensive test suite
    ├── Models/         # Model tests
    ├── Services/       # Service layer tests
    └── TestHelpers/    # Test utilities and runners
```

### Key Components

#### Storage Layer
- **FileStorageService**: Generic file operations with backup/recovery
- **CategoryStorageService**: Specialized category persistence
- Automatic backup creation and cleanup
- Error handling with custom StorageError types

#### AI Services
- **AIModel**: Core AI categorization engine
- **CategorizationEngine**: Advanced categorization logic
- **CategorizationCoordinator**: Orchestrates categorization workflow

#### Utilities
- **HashtagParser**: Parses hashtag-based category assignments
- **MessageParser**: Extracts main content and subcontent from messages
- **URLContentAnalyzer**: Analyzes URL content for categorization

## Requirements

- iOS 15.0+
- Xcode 15.0+
- Swift 5.9+

## Installation

1. Clone the repository:
   ```bash
   git clone https://github.com/shikha-aggarwal/Pebl.git
   cd Pebl
   ```

2. Open `Pebl.xcodeproj` in Xcode

3. Build and run the project:
   - Select your target device/simulator
   - Press Cmd+R to build and run

## Usage

### Basic Usage
1. Enter your message in the text field
2. Tap "Store" to let Pebl intelligently categorize it
3. View your categorized items by selecting categories
4. Toggle completion status by tapping the checkbox

### Advanced Features
- **Manual Categorization**: Use `#CategoryName` at start or end of message
- **Subcategories**: Use `#Category/Subcategory` format
- **URL Messages**: Paste URLs for automatic content extraction
- **Batch Operations**: Use "Clear Completed" to remove finished items

### Hashtag Examples
- `#shopping Buy groceries` → Shopping category
- `Watch Inception #movies` → Movies to Watch category
- `#work/meetings Team standup` → Work category, Meetings subcategory

## Testing

The app includes comprehensive test coverage:

```bash
# Run all tests (if Xcode project configured)
xcodebuild test -scheme Pebl -destination 'platform=iOS Simulator,name=iPhone 16'

# Run custom test framework
swift Tests/UnitTests/TestHelpers/TestRunner.swift
```

### Test Coverage
- ✅ AI categorization logic
- ✅ Storage service operations
- ✅ Message parsing and completion
- ✅ Edge case handling
- ✅ UI component functionality

## Configuration

App settings can be modified in `Pebl/App/Configuration/AppConfig.swift`:
- Subcategory creation thresholds
- Default categories
- Storage settings
- AI model parameters

## Contributing

This project follows L6 engineering standards:
- Clean architecture with separation of concerns
- Comprehensive test coverage
- Proper error handling
- Configuration-driven development
- Modular, reusable components

## License

This project is proprietary software. All rights reserved.
